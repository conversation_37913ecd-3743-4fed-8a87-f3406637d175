#grand-header
  display: flex
  align-item: center
  $grand-height: 3rem
  .logo
    height: $grand-height
    width: $grand-height
  .text
    font-size: $grand-height
    // Magic happens here
    display: inline
    position: relative
    bottom: 10%
    .slash
      position: relative
      bottom: 2%
    .logo-text
      //
    .description
      font-style: italic
      font-size: .5em
  @media screen and (max-width: $smallScreenWidth)
    $small-grand-height: 2.2rem
    .logo
      height: $small-grand-height
      width: $small-grand-height
    .text
      font-size: $small-grand-height

.search-grand-header
  margin-top: .5rem

.section
  margin: 1rem 0
  border-top: 2px solid grey
  padding-top: .3rem
  .marker
    position: absolute
    margin: 0
    padding-top: .3rem
    max-width: 9rem

    font-weight: 600
    font-size: 1.25em
  .content
    padding-left: 10rem
    padding-top: .4rem
    a
      display: block
      position: relative
      // Hack for Zola's Markdown renderer
      margin: 0
      left: -1rem
    p
      margin: 0 0 1rem 0
      padding-left: 1rem
  @media screen and (max-width: $smallScreenWidth)
    .marker
      position: relative
      max-width: 100%
    .content
      padding: .5rem 0
