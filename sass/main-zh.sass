$sansSerifFonts : 'Hiragino Sans GB', 'Noto Sans CJK SC', 'Noto Sans SC', 'Microsoft YaHei', sans-serif
$serifFonts : 'Noto Serif CJK SC', 'Songti SC', 'Noto Serif SC', 'Microsoft YaHei', serif
$monospaceFonts : 'Source Code Pro', 'Hiragino Sans GB', 'Noto Sans CJK SC', 'Microsoft YaHei', monospace
$singleColWidth: 65rem
$smallScreenWidth: 35rem

@import "./_main.sass"
@import "./_typography.sass"
@import "./_article.sass"
@import "./_index.sass"
@import "./_components.sass"
@import "./_hack.sass"
@import "./_color.sass"
@import "./_color_dark.sass"

// Here's the language specifc settings
header
  p#description
    // We don't do that here
    font-style: normal

#grand-header
  .text .description
    font-style: normal

nav
  h2
    font-weight: 600
