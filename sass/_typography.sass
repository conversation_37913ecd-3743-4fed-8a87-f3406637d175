body
  // Nerdy stuff
  text-rendering: optimizeLegibility
  font-feature-settings: 'liga' 1
  // Stupid Safari...
  text-size-adjust: none

  font-size: 1.1rem
  font-weight: 400
  line-height: 1.5
  hyphens: auto

h1, h2, h3, h4, h5, h6
  font-family: $serifFonts

h1
  font-size: 1.4em

h2
  font-size: 1.25em

h3
  font-size: 1.1em

a
  transition-duration: .1s
  transition-property: background
  text-decoration: none

// lists
ul
  list-style-type: square
  li
    margin: .2rem 0

blockquote
  margin: 0
  padding: .5rem 1.5rem
  border-left: 2px solid grey
  *
    margin: 0

code
  font-family: $monospaceFonts
  font-size: .95em

pre
  overflow-x: auto
  padding: 1rem
  line-height: 1.2
  code
    // So we can see non-colored text
    color: #c0c5ce
    background-color: transparent

// Table formatting
table
  display: block
  margin: .5rem 0
  max-width: 100%
  overflow-x: auto 
  border-collapse: collapse

  th, td
    padding: .75rem
    min-width: 5em
  th
    border-width: 0 0 2px 0
    border-style: solid
  td
    border-width: 0 0 1px 0
    border-style: solid

.footnote-definition
  .footnote-definition-label
  p
    display: inline
