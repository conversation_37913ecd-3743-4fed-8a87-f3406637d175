html
  scroll-behavior: smooth
  height: 100%
  margin: 0
  padding: 0
  text-rendering: optimizelegibility
  text-size-adjust: none

body
  font-family: $serifFonts
  background-color: white
  margin: 0 auto
  padding: 5rem 20rem 2rem 1rem

  max-width: 45rem
  min-height: calc(100% - 7rem)
  position: relative
  @media screen and (max-width: $singleColWidth)
    max-width: 54rem
    padding: 1rem
    min-height: calc(100% - 2rem)
  main
    padding-bottom: 2rem

#search 
  position: absolute
  display: block
  right: 0
  width: 18rem
  padding: 0 .5rem

  font-family: $sansSerifFonts
  // Use in conjunction with nav
  z-index: 2
  @media screen and (max-width: $singleColWidth)
    position: relative
    width: 100%
    padding: 0 0 1rem 0
  #search-results
    max-height: 30rem
    overflow-y: auto
    // Due to padding on search bar
    margin-top: -1.5rem
  .search-result
    display: block
    font-size: 1em
    padding: .2rem .3rem
    border-width: 0 2px 2px 2px
    border-style: solid
    .title
      display: block
    .context
      font-size: .8em

input[type="search"]
  // To make Mobile Safari happy
  appearance: none
  -webkit-appearance: none
  border-radius: 0

  margin-top: -0.1rem
  margin-bottom: 1.5rem
  display: block
  width: 100%
  height: 2.05rem
  padding: 0 .4rem
  border-width: 0px 0px 2px 0px
  border-style: solid

header
  position: relative

  #position-bar
    font-size: 1.05em
    font-family: $sansSerifFonts
    padding-bottom: .2rem
    border-bottom: 1px dotted grey

    display: flex
    flex-wrap: wrap
    a,span
      margin-right: .2rem
    a.section-link
      flex-shrink: 0
    .right
      margin-left: auto
      flex-shrink: 0
  h1#title
    font-size: 1.8em
    margin: .6rem 0 0 0
  p#description
    margin: .2rem 0
    font-size: 1.2em
    font-family: $serifFonts
    font-style: italic

main
  position: relative
  margin-bottom: 2.1rem

nav
  position: absolute
  display: block
  top: 0
  left: 100%
  width: 18rem
  padding: 0 1.5rem
  z-index: 1

  &.section-nav
    // If it is section nav (which is located at bottom)
    // Will always be a block at bottom
    // (Otherwise the layout will break to hell)
    @media screen and (max-width: $singleColWidth)
      float: none
      position: relative
      float: none
      width: calc(100% - 2rem - 2px)
      margin: 2rem 0 1rem 0

      // Add a box to differenciate
      padding: 1rem
      border: 1.5px solid grey
  @media screen and (max-width: $singleColWidth)
    // Reset the layout for wide screen
    position: relative
    top: 0
    left: 0

    float: right
    margin: 0 1rem .5rem 1rem
  @media screen and (max-width: $smallScreenWidth)
    position: relative
    float: none
    width: 100%
    margin: 1rem 0
    padding: 0
  .nav-section
    margin-bottom: 1rem
  h2
    margin: 0
    font-size: 1.1em
    font-weight: 300
  a
    display: block
  .first-level-header
    &:before
      content: "> "

footer
  position: absolute
  font-size: .9em
  bottom: 1rem
  opacity: .7
  p
    // Set right margin to prevent overlapping with back-to-top
    margin: 0 3.8rem 0 0

#back-to-top
  $bottonSize: 2.5rem
  position: fixed
  height: $bottonSize
  width: $bottonSize
  right: 1rem
  bottom: 1rem
  overflow: hidden
  z-index: 2
  .icon
    display: block
    font-size: 2rem
    line-height: 2.6rem
    text-align: center

