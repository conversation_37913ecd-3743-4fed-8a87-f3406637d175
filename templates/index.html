{% extends "base.html" %}

{# Default head block is good enough here. #}

{% block search %}
  <div id="search" class="search-grand-header">
    {% set search_msg = trans(key="search_placeholder", lang=lang) %}
    <input id="search-bar" type="search" placeholder="{{ search_msg }}">
    <div id="search-results">
      <!-- Search Results will be displayed here -->
    </div>
  </div>
{% endblock search %}

{% block header %}
  <div id="grand-header">
    <div class="grand-title">
      <img class="logo" src="/img/aosc.png">
      <div class="text">
        <span class="slash">/</span><span class="logo-text">{{ config.extra.logo_text }}</span>
        <span class="description">{{ trans(key="description", lang=lang) }}</span>
      </div>
    </div>
  </div>
{% endblock header %}

{% block main %}
  {{ section.content | safe }}

  <nav class="section-nav">
    <div class="nav-section">
      <h2>{{ trans(key="also_available_in", lang=lang) }}</h2>
      {% for translated in section.translations %}
        <a href="{{ translated.permalink }}">{{ trans(key=translated.lang, lang=lang) }}</a>
      {% endfor %}
    </div>

    <h2>{{ trans(key="on_this_wiki", lang=lang) }}</h2>
    {% for subsection_path in section.subsections %}
      {% set subsection = get_section(path=subsection_path, metadata_only=true) %}
      <a href="{{ subsection.permalink }}">
        <span style="color: grey">> </span>
        {{ subsection.title }}
      </a>
    {% endfor %}

    {% for post in section.pages %}
      <a href="{{ post.permalink }}">{{ post.title }}</a>
    {% endfor %}
  </nav>
{% endblock main %}
