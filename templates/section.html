{% import "macros.html" as macros %}
{% extends "base.html" %}


{% block head %}
  {{ macros::head(title=section.title, url=section.permalink, description=section.description) }}
{% endblock head %}

{% block header %}
  <div id="position-bar">
    {# Navigation #}
    {% for ancestor in section.ancestors %}
      {% set an_section = get_section(path=ancestor, metadata_only=true) %}
      <a class="section-link" href="{{ an_section.permalink }}">{{ an_section.title }}</a>
      {% if not loop.last %}
        <span>/</span>
      {% else %}
        <span>/</span>
        <a class="section-link" href="{{ section.permalink }}">{{ section.title }}</a>
        <span>/</span>
      {% endif %}
    {% endfor %}

    {{ macros::language_select(source=section, lang=section.lang) }}
  </div>

  <h1 id="title">{{ section.title }}</h1>
  <p id="description">{{ section.description }}</p>
{% endblock header %}

{% block main %}
  {# if this is a simple list section #}
  {% if section.extra.list_section %}
    {{ section.content | safe }}
    <ul style="list-style-position: inside; padding-left: 0">
      {% for post in section.pages %}
        <li>
          <a href="{{ post.permalink }}">{{ post.title }}</a>
        </li>
      {% endfor %}
    </ul>

    <nav class="section-nav">
      <b>{{ trans(key="subsecs_in_this_sec", lang=section.lang) }}</b>
      {% if section.subsections | length == 0 %}
        <i style="display: block">None</i>
      {% endif %}

      {% for subsection_path in section.subsections %}
        {% set subsection = get_section(path=subsection_path, metadata_only=true) %}
        <a href="{{ subsection.permalink }}">
          <span style="color: grey">> </span>
          {{ subsection.title }}
        </a>
      {% endfor %}
    </nav>
    
  {% else %}
    <nav class="section-nav">
      <b>{{ trans(key="contents_in_this_section", lang=section.lang) }}</b>
      {% for subsection_path in section.subsections %}
        {% set subsection = get_section(path=subsection_path, metadata_only=true) %}
        <a href="{{ subsection.permalink }}">
          <span style="color: grey">> </span>
          {{ subsection.title }}
        </a>
      {% endfor %}

      {% set pages = section.pages | sort(attribute="title") %}
      {% for post in pages %}
        <a href="{{ post.permalink }}">{{ post.title }}</a>
      {% endfor %}

    </nav>

    <article>
      {{ section.content | safe }}
    </article>
  {% endif %}
{% endblock main %}
