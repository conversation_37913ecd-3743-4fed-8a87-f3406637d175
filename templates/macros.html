{% macro head(title, url, description) %}
  <title>{{ title }} - {{ trans(key="title", lang=lang) }}</title>
  <link rel="icon" href="{{ config.extra.logo }}">

  <meta property="og:title" content="{{ title }} - {{ trans(key="title", lang=lang) }}"/>
  <meta property="og:type" content="article" />
  <meta property="og:url" content="{{ url }}" />
  <meta property="og:image" content="{{ config.extra.logo }}" />
  <meta property="og:description" content="{{ description }}"/>
  <meta name="description" content="{{ description }}">
{% endmacro head %}

{% macro language_select(source) %}
  <div class="right">
    {% if source.translations | length > 1 %}
      {{ trans(key="also_available_in", lang=lang) }}
      {% for t in source.translations %}
        {% if t.lang != lang %}
        <a href="{{ t.permalink }}">{{ trans(key=t.lang, lang=lang) }}</a>
        {% endif %}
      {% endfor %}
    {% endif %}
  </div>
{% endmacro language_select %}

