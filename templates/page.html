{% import "macros.html" as macros %}

{% extends "base.html" %}

{% block head %}
  {{ macros::head(title=page.title, url=page.permalink, description=page.description) }}
{% endblock head %}

{% block header %}
  <div id="position-bar">
    {# Navigation #}
    {% for ancestor in page.ancestors %}
      {% set section = get_section(path=ancestor, metadata_only=true) %}
      <a class="section-link" href="{{ section.permalink }}">{{ section.title }}</a>
      {% if not loop.last %}
        <span>/</span>
      {% else %}
        <span>/</span>
        <a class="section-link" href="{{ page.permalink }}">.</a>
      {% endif %}
    {% endfor %}

    {{ macros::language_select(source=page, lang=page.lang) }}
  </div>

  <h1 id="title">{{ page.title }}</h1>
  <p id="description">{{ page.description }}</p>
{% endblock header %}

{% block main %}
  <nav>
    <b>{{ trans(key="on_this_page", lang=page.lang) }}</b>
    {% for header in page.toc %}
      <a class="first-level-header" href="{{ header.permalink }}">{{ header.title }}</a>
      <div style="margin-left: 1.3rem">
        {% for header in header.children %}
          <a href="{{ header.permalink }}">{{ header.title }}</a>
        {% endfor %}
      </div>
    {% endfor %}
  </nav>
  <article>
    {% if page.extra.page_hack %}
    <div class="{{ page.extra.page_hack }}">
      {{ page.content | safe }}
    </div>
    {% else %}
    {{ page.content | safe }}
    {% endif %}
  </article>
{% endblock main %}
