{% import "macros.html" as macros %}

<!DOCTYPE html>
<html lang="{{ lang }}">
  <head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
    {% if lang == "zh" %}
      <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500&family=Noto+Serif+SC:wght@400;600;700&display=swap" rel="stylesheet"> 
    {% else %}
      <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:ital,wght@0,400;0,600;1,400;1,600&family=Source+Serif+Pro:ital,wght@0,400;0,600;1,400;1,600&display=swap" rel="stylesheet"> 
    {% endif %}
     <link href="https://fonts.googleapis.com/css2?family=Source+Code+Pro:ital@0;1&display=swap" rel="stylesheet"> 
	<link rel="stylesheet" href="/main-{{ lang }}.css">

    {% block head %}
    {{ macros::head(title=trans(key="index_page", lang=lang), url=config.base_url, description=config.description) }}
    {% endblock head %}
  </head>

  {# For Netsurf #}
  <a name="top"></a>

  <body>
    {% block search %}
    <div id="search">
      {% set search_msg = trans(key="search_placeholder", lang=lang) %}
      <input id="search-bar" type="search" placeholder="{{ search_msg }}">
      <div id="search-results">
        <!-- Search Results will be displayed here -->
      </div>
    </div>
    {% endblock search %}

    <header>
      {% block header %}
      {% endblock header %}
    </header>

    <main>
      {% block main %}
      {% endblock main %}
    </main>
    <a id="back-to-top" href="#top">
      <div class="icon">↑</div>
    </a>
  </body>

  <footer>
    {% block footer %}
    <p>{{ trans(key="footer_msg", lang=lang) | markdown | safe }}</p>
    {% endblock footer %}
  </footer>

  <script src="/elasticlunr.min.js"></script>
  <script src="/search.min.js"></script>
</html>
