var dict={zh:{'not-found':'找不到相符的内容或信息','try-again':'请输入其他关键字重试。','loading-search':'正在加载搜索索引','plz-wait':'请稍候……',loaded:'搜索索引加载完成','you-can-search':'输入关键字即可搜索。','load-error':'搜索加载失败','check-network':'请刷新后重试。'},en:{'not-found':'Nothing found','try-again':'Try to search again with another keyword.','loading-search':'Initializing Search','plz-wait':'Please wait...',loaded:'Search Initialisation Complete','you-can-search':'You can type to search now.','load-error':'Search Initialisation Failed','check-network':'Please try to refresh the page and try again.'}};function debounce(func,wait){var timeout;return function(){var context=this;var args=arguments;clearTimeout(timeout);timeout=setTimeout(function(){timeout=null;func.apply(context,args);},wait);};}function makeTeaser(body,terms){var TERM_WEIGHT=40;var NORMAL_WORD_WEIGHT=2;var FIRST_WORD_WEIGHT=8;var TEASER_MAX_WORDS=30;var stemmedTerms=terms.map(function(w){return window.elasticlunr.stemmer(w.toLowerCase());});var termFound=false;var index=0;var weighted=[];var sentences=body.toLowerCase().split('. ');for(var i in sentences){var words=sentences[i].split(' ');var value=FIRST_WORD_WEIGHT;for(var j in words){var word=words[j];if(word.length>0){for(var k in stemmedTerms){if(window.elasticlunr.stemmer(word).startsWith(stemmedTerms[k])){value=TERM_WEIGHT;termFound=true;}}weighted.push([word,value,index]);value=NORMAL_WORD_WEIGHT;}index+=word.length;index+=1;}index+=1;}if(weighted.length===0){return body;}var windowWeights=[];var windowSize=Math.min(weighted.length,TEASER_MAX_WORDS);var curSum=0;for(var i1=0;i1<windowSize;i1++){curSum+=weighted[i1][1];}windowWeights.push(curSum);for(var i2=0;i2<weighted.length-windowSize;i2++){curSum-=weighted[i2][1];curSum+=weighted[i2+windowSize][1];windowWeights.push(curSum);}var maxSumIndex=0;if(termFound){var maxFound=0;for(var i3=windowWeights.length-1;i3>=0;i3--){if(windowWeights[i3]>maxFound){maxFound=windowWeights[i3];maxSumIndex=i3;}}}var teaser=[];var startIndex=weighted[maxSumIndex][2];for(var i3=maxSumIndex;i3<maxSumIndex+windowSize;i3++){var word=weighted[i3];if(startIndex<word[2]){teaser.push(body.substring(startIndex,word[2]));startIndex=word[2];}if(word[1]===TERM_WEIGHT){teaser.push('<b>');}startIndex=word[2]+word[0].length;teaser.push(body.substring(word[2],startIndex));if(word[1]===TERM_WEIGHT){teaser.push('</b>');}}teaser.push('…');return teaser.join('');}function createSearchResult(title,context,href){var item=document.createElement('a');item.classList.add('search-result');item.href=href;var title_span=document.createElement('span');title_span.classList.add('title');title_span.innerHTML=title;var context_span=document.createElement('span');context_span.classList.add('context');context_span.innerHTML=context;item.appendChild(title_span);item.appendChild(context_span);return item;}function searchText(){if(search_bar.value===''){search_results.innerHTML='';return;}var index=window.elasticlunr.Index.load(window.searchIndex);var results=index.search(search_bar.value,options);search_results.innerHTML='';if(results.length===0){var sorry=createSearchResult(dict[lang]['not-found'],dict[lang]['try-again'],'#');search_results.appendChild(sorry);return;}var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;try{for(var _iterator=results[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var i=_step.value;var teaser=makeTeaser(i.doc.body,search_bar.value.split(' '));var item=createSearchResult(i.doc.title,teaser,i.ref);search_results.appendChild(item);}}catch(err) {_didIteratorError=true;_iteratorError=err;}finally{try{if(!_iteratorNormalCompletion&&_iterator["return"]!=null){_iterator["return"]();}}finally{if(_didIteratorError){throw _iteratorError;}}}}var options={bool:'AND',fields:{title:{boost:2},body:{boost:1}}};var search_bar=document.getElementById('search-bar');var search_results=document.getElementById('search-results');var lang='en';var search_index_loading=false;search_bar.addEventListener('input',debounce(searchText,200));search_bar.addEventListener('focus',function(){if(search_index_loading){return;}if(!window.searchIndex){var imported=document.createElement('script');imported.src='/search_index.'+lang+'.js';document.head.appendChild(imported);search_index_loading=true;var pending=createSearchResult(dict[lang]['loading-search'],dict[lang]['plz-wait'],'#');search_results.appendChild(pending);imported.onload=function(){var loaded=createSearchResult(dict[lang]['loaded'],dict[lang]['you-can-search'],'#');search_results.innerHTML='';search_results.appendChild(loaded);setTimeout(searchText,1500);};imported.onerror=function(message){console.error(message);var load_error=createSearchResult(dict[lang]['load-error'],dict[lang]['check-network'],'#');search_results.innerHTML='';search_results.appendChild(load_error);};return;}});

