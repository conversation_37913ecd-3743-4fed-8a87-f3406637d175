trigger:
- master

pool:
  vmImage: 'ubuntu-latest'

steps:
- task: InstallSSH<PERSON><PERSON>@0
  inputs:
    knownHostsEntry: '$(UPLOAD_HOST) ecdsa-sha2-nistp256 AAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlzdHAyNTYAAABBBGan/F63NCg5xLo6ooCWfHoXfaDw4mDFwZC/0kMdgsBrwxjyI6sjWexdWDz1pntsHbXjoc4jMOqoU9ZIyk26NE8='
    sshPublicKey: 'ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIAUiF8B79DhQdFagc6zg+5VgWeSIXFWYYn/fTE7ltt8J aosc@aosc'
    sshPassphrase: '$(KEY_PASSWD)'
    sshKeySecureFile: 'upload_key'
  condition: ne(variables['Build.Reason'], 'PullRequest')

- bash: ./.ci/ci.sh
  displayName: 'Install Zola'

- bash: ./build.sh
  displayName: 'Build the pages'

- bash: "rsync -rlOvhze ssh --progress public/* 'upload@$(UPLOAD_HOST):/srv/aosc-wiki/' && echo \"Visit your site at: https://wiki.aosc.io\""
  displayName: 'Upload the files'
  condition: and(ne(variables['Build.Reason'], 'PullRequest'), succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/master'))
