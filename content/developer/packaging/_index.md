+++
title = "Packaging"
+++

Welcome! If you are reading this, you are probably interested in contributing to the AOSC OS project. This guide will guide you through all the tools and techniques you need to create, update, and maintain AOSC OS packages.

If you are new to packaging for AOSC OS, please first take a look at the [Intro to Package Maintenance: Basics](@/developer/packaging/basics.md). After that, read [Intro to Package Maintenance: Advanced Techniques](@/developer/packaging/advanced-techniques.md) for some very common techniques used in day-to-day packaging.

In order to ensure the quality of our packages (and reduce wtf moments for other developers), please follow these guidelines.

- [AOSC OS Topic-Based Maintenance Guidelines](@/developer/packaging/topic-based-maintenance-guideline.md)
- [AOSC OS Package Styling Manual](@/developer/packaging/package-styling-manual.md)
- [Making Use of the Automated Maintenance Infrastructure](@/developer/packaging/buildit-bot.md)
- [Revision Marking Guidelines for Topic Packages](@/developer/packaging/topic-version-suffix.md)
- [AOSC OS Feature Marking Guidelines](@/developer/packaging/feature-marking-guidelines.md)

Also, you may want to read these documentations about the tools we use.
- [Autobuild3 Manual](@/developer/packaging/autobuild3-manual.md)
- [Autobuild4 Manual](@/developer/packaging/autobuild4/_index.md)
- [Ciel Manual](@/developer/packaging/ciel-manual.md)

Other useful information of this section:
- [Common Issue and Fixes](@/developer/packaging/quirks.md) describes some issue you may face during packaging and corresponding quirks.
- [Exceptions to the Update Cycles](@/developer/packaging/cycle-exceptions.md)
- [List Of Package Issue Codes](@/developer/packaging/qa-issue-codes.md)
- [Known Patch Release Rules](@/developer/packaging/known-patch-release-rules.md)
- [.NET Lifecycle Policy](@/developer/packaging/dotnet.md)
