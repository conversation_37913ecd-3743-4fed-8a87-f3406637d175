+++
title = "打包"
+++

您好呀！如果您正在阅读这篇文章，您可能希望参与到 AOSC OS 开发当中。本系列指南将指导您如何创建、更新和维护 AOSC OS 软件包。

如果您还没有为 AOSC OS 打过包，我们推荐您首先阅读 [软件包维护入门之基础篇](@/developer/packaging/basics.zh.md)。接下来，您可以继续阅读 [软件包维护入门之进阶篇](@/developer/packaging/advanced-techniques.zh.md) 了解在实际打包过程中经常使用的一些的技术。

为了确保构建好的软件包的质量，请遵循下面的这些指南：

- [AOSC OS 主题制维护指南 (English)](@/developer/packaging/topic-based-maintenance-guideline.md)
- [AOSC OS 软件包样式指南](@/developer/packaging/package-styling-manual.zh.md)
- [使用自动化设施构建软件包](@/developer/packaging/buildit-bot.zh.md)
- [测试源内软件包迭代版本规范](@/developer/packaging/topic-version-suffix.zh.md)
- [AOSC OS 系统特性标记规范](@/developer/packaging/feature-marking-guidelines.zh.md)

想要进一步了解我们在打包中常用的一些应用程序，可以阅读下面的文档：

- [Autobuild3 帮助文档](@/developer/packaging/autobuild3-manual.md)
- [Autobuild4 帮助文档](@/developer/packaging/autobuild4/_index.zh.md)
- [Ciel 帮助文档](@/developer/packaging/ciel-manual.md)

您可能也会对本版块的其它文章感兴趣：

- [常见问题及其解决方案](@/developer/packaging/quirks.zh.md)
- [更新周期例外说明](@/developer/packaging/cycle-exceptions.md)
- [软件包质量测试错误代码注解](@/developer/packaging/qa-issue-codes.zh.md)
- [稳定分支补丁级更新规则说明](@/developer/packaging/known-patch-release-rules.md)
- [.NET 生命周期策略](@/developer/packaging/dotnet.zh.md)
- [glibc HWCAPS 子架构构建指南](@/developer/packaging/hwcaps-packaging-guide.zh.md)
