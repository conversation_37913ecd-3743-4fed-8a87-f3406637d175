+++
title = "ACBS - Introduction"
+++

Introduction
============

What is ACBS?
-------------

AC<PERSON> (AutoBuild CI Build System) is a re-implementation of the original
ABBS (AutoBuild Build Service) in Python. It's a smart upper structure
of the basic Autobuild3 multi-function package builder.

What happend to ABBS, its ancestor?
-----------------------------------

ACBS was first created with an intent to extend upon ABBS and improve
its sorry reliability and agility. ACBS supports multi-tree layouts and
proper environment handling, providing a more reliable and efficient
experience. Comparing to its ancestor, it improved reliability and added
a few more features like an automated dependency resolver.

How to install or deploy it?
----------------------------

<PERSON>BS is only designed to work with Autobuild3 and under Unix-like
environment. The detailed instructions can be found at `install`.
