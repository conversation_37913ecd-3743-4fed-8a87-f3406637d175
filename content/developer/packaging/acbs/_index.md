+++
title = "ACBS"
+++

Welcome to acbs’s documentation!
================================

ACBS - The current generation of building toolkit for AOSC OS written in
Python.

Contents:

-   Introduction
    -   What is ACBS?
    -   What happend to ABBS, its ancestor?
    -   How to install or deploy it?
-   Installation
    -   Get Started
    -   Requirements
    -   Initial configurations
-   Director’s Cut (DX) Version
    -   Background
    -   Version Number
    -   Behavioral Changes
-   ACBS API Reference
    -   ACBS base
    -   ACBS deps
    -   ACBS utils
-   Specification Files
    -   defines
    -   spec
-   Appendix
    -   Supported VCS
    -   Acceptable Prefixes for "SRCS"
    -   Supported Checksum (Hashing) Algorithm
