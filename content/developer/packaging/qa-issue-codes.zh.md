+++
title = "软件包问题代码速查表"
description = "了解每个问题代码背后的含义"
date = 2020-05-04T03:36:05.233Z
[taxonomies]
tags = ["dev-sys"]
+++

# 问题代码

为了帮助开发者定位问题，我们将问题代码分为了下面四类： 

- "E" 开头的问题代码意味着这是一个错误。
- "W" 开头的问题代码意味着这是一个警告。

## 第一类：元数据

| 代码 | 含义 |
|-----------|----------------------|
| E101 | `spec` 中有语法错误 |
| E102 | `defines` 中有语法错误 |
| E103 | 软件包名称不合法 |
| E104 | 软件包类别不合法 |
| W111 | 软件包可能过期了 |
| W112 | `SRCTBL` 使用了 HTTP |
| W113 | `SRCTBL` 没有对应的 `CHKSUM` |
| W121 | 提交信息未经格式化 |
| W122 | 单个提交对多个软件包做了修改 |
| W123 | 最近使用了强制推送 |

## 第二类：构建流程

| 代码 | 含义 |
|-----------|----------------------|
| E201 | 无法获取源码 |
| E202 | 无法获取依赖 |
| E211 | 构建失败（FTBFS） |
| E221 | 无法启动生成的可执行文件 |
| W222 | 某个功能不起作用，或单元测试未通过 |

## 第三类：产品交付（DEB 软件包）

| 代码 | 含义 |
|-----------|----------------------|
| E301 | .deb 文件损坏 |
| E(W)302 | .deb 文件过小 |
| E303 | .deb 文件名或存储路径不合法 |
| E311 | .deb 维护者信息不合法 |
| E321 | .deb 中发现放置在非法位置的文件 |
| E(W)322 | .deb 中发现空文件 |
| E(W)323 | .deb 中发现带有错误归属信息的文件 |
| E(W)324 | .deb 中发现带有错误权限信息的文件 |
| E325 | .deb 中某个文件不存在 |
| W331 | 发现超过一年未更新的软件包 |

## 第四类：依赖项

| 代码 | 含义 |
|-----------|----------------------|
| E401 | 无法满足 `BUILDDEP` |
| E402 | 软件包树中发现重复软件包 |
| E411 | 无法满足 `PKGDEP` |
| E412 | 软件仓库中发现重复软件包 |
| E421 | 文件冲突 |
| E431 | 无法满足库文件版本依赖 |
| E432 | `PKGDEP` 缺失库文件依赖 |