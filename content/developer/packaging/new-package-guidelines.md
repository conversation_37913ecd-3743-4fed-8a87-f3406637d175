+++
title = "Guidelines on Introducing New Packages"
description = "As Simple as Yes or No"
date = 2020-08-03T12:01:46.691Z
+++

*Fucking hell. If the upstream lets you, then package; no means no. Got it?*

When introducing new packages to the AOSC OS repository, only one consideration need apply: That the upstream of the software package permits redistribution in such a fashion that AOSC OS maintainers could package and distribute copies of said software.

AOSC OS maintainers are not responsible for any particular user's application of a software package, and thus will not serve as a basis for accepting or rejecting package requests.

When in doubt, discuss on a case-by-case basis.
