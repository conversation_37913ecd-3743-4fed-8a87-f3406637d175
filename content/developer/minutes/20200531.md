+++
title = "Contributor Minutes on 2020-05-31"
description = ""
date = 2020-05-31T06:06:57.176Z
[taxonomies]
tags = ["minutes"]
+++

Attendees
---------
- <PERSON><PERSON>g Bai
- Zixing Liu
- <PERSON><PERSON>
- <PERSON> "<PERSON><PERSON>" <PERSON>l
- <PERSON>aph
- Lion (late)

Agenda
------
- [A<PERSON><PERSON>-ification (Continued)](#aosc-moe-ification-continued)
- [OSPP](#ospp)
- [Autobuild3](#autobuild3)
- [AOINB](#aoinb)
- [AOSCBootstrap and DeployKit (improvised)](#aoscbootstrap-and-deploykit)

Minutes
-------

## A<PERSON><PERSON>-ification (Continued)

- <PERSON>: On the subject of species, should it be more humanoid or animal like?
    - <PERSON>: I prefer humanoid design, what do you guys think?
    - <PERSON><PERSON>: Human.
    - Staph: Not animal.
- <PERSON>: On the subject of character features?
	- <PERSON>: His (<PERSON>) interpretation is not very accurate.
	- <PERSON>: The character feature should not be "so mild" in the sense.
	- <PERSON><PERSON>: It's just designer's interpretation of our community which he did not know too much about us.
	- <PERSON>: Yes. That's why we are discussing about it.
	- <PERSON><PERSON>: Is there anything about this topic before?
	- <PERSON>: Yes. In the last week's minutes.
	- <PERSON>: She should be shy.
	- <PERSON>: However "don't even communicate with normal people" is not very accurate.
	- Staph: Like Cipher suite negotitation?
	- <PERSON>: Mostly shy, but when met with a person with common interests, she will be very talk-active.
- Bai: Inclusion, the mascot: 1. Not so eccentric, she's a normal person; 2. Shy, but when met with a person with common interests, she will be talk-active and filled with lame jokes.

## <PERSON>PP

(Going over the promotional/introductional slides together)

## Autobuild3

- Bai: There have been some changes made to `autobuild3`. There might be more errors incoming.
- Bai: I made a QA branch dedicated to QA checks. QA checks are divided into pre- and post-build checks.
- Bai: Also I will implement test functionality into `autobuild3`. It will run all the scripts under `autobuild/tests/` and we may have some standard template tests implemented.
- Liu: I think we better off only provide some functions exported from `autobuild3` to be used `autobuild/tests` scripts. Since there will be more consistency issues than build systems.
- Bai: Okay, we will hold-off this function for now.

## AOINB

- Leo: The current AOINB design is like this: You have the preprocessor which calculates the dependencies using abbs-meta, then you have the dispatcher, which dispatch the tasks to the agents and communicates with the builder on the agents.
- Leo: I wonder if adding a database will be more convient when dealing with event changes like a new agent is available or a new job is queued
- Liu: Sounds like a work for Redis.
- Leo: It may just be an in-memory database.
- Liu: Or you can do without a database, just make some data structures maintained in the memory.
- Liu: Now I have a question: what if we have three packages, say, A, B and C. C depends on B and B depends on A. Now, the job was dispatched to Bai's Ryzen computer, and the builder has already completed A and B, and the builder is currently building package C. Then I realized I made a mistake on package A and I pushed a fix. What would happen?
- Leo: You mean to implement an interrupt function?
- Liu: From my understanding, the preprocessor will resolve the dependencies to A -> B -> C when dispatch the previous batch of jobs. When the fix was pushed, the preprocessor will resolve the dependency to only A, and then the dispatcher willl probably dispatch the job to another agent, causing B and C to be broken packages.
- Leo: So interrupt the jobs and stash them for later when A finishes?
- Liu: My idea is to replace the A in A -> B -> C to become A' -> B -> C.
- Leo: That would be very difficult.
- Lion: But interrupting/canceling the jobs affected is too much.
- Liu, Leo, Yhi and Lion: \*Bikeshedding intensifies\*
- Lion: We can implement a system that will ask the developer to pick a resolution before taking any actions.

## AOSCBootstrap and DeployKit

- Bai: Can DeployKit make use of AOSCBootstrap when installing from scratch?
- Yhi: Doable. It's just commandline invokation.
- Bai: Okay.

Actionables
-----------

- Liu: Reply the email to Tyson with the conclusions above after the meeting. Mention the name of the mascot.
- Yhi: Ready the slides and speaker notes. Dry-run on next Wednesday. Add the mentions of Icenowy's efforts on Allwinner hardwares.
- Staph: Prepare the shorter version of his OSPP project summary. Due next Wednesday.
