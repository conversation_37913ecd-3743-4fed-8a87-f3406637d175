+++
title = "Contributor Minutes on 2022-04-02"
description = ""
date = 2022-04-02
[taxonomies]
tags = ["minutes"]
+++

Contributor Minutes on 2022-04-02
=================================

Attendees
---------
- <PERSON><PERSON><PERSON> (Bai)
- cth451
- <PERSON><PERSON><PERSON> (<PERSON>)
- Neruthes

Agenda
======

- [OSPP 2022 Preparations](#ospp-2022-preparations)

OSPP 2022 Preparations
=======================================
- Neruthes: Let's talk about the [RFC documentation](https://repo.aosc.io/misc/artifacts/miscdoc/ospp/Plan-2022.pdf) I have made for this year's OSPP project.
    - <PERSON>: I would recommend that you make adjustments to this documentation while OSPP 2022 is in progress so that we can get better understandings on what to change.
    - Bai: Currently, the general direction of this documentation is good. However, it could not be determined whether we should force all the prospective mentors to write long paragraphs of design documents and the likes.
    - <PERSON>: I am also not sure whether we should just send a copy of what we should submit to the OSPP committee to the community channels, or we should alter the documentation to some extent.
    - Neruthes: I think all the documentation that submitted to the OSPP committee should also be published in the community channels for transparency. But in which process, that could be a question for later.
    - Bai: Also all the deadlines on this document are bit too tight. Should I really have to finish the guidelines by April 6th?
    - Neruthes: These dates are tentative, and should not be considered final.
- Bai: Anyhow, let's talk about the projects we are going to present to the committee.
    - Bai: This year's OSPP had some changes, namely the difficulty levels are now (1) basic and (2) advanced. Also, one mentor could mentor at most one project this year.
    - Liu: Yes, probably they had some financial issues. And the number of projects each community could propose are also limited this year.
    - Bai: The difficulty classification guideline is also clear as mud. Only optimization work could be considered for advanced projects seem a bit unfair to be honest.
    - Liu: And according to the classification guideline, feature additions are considered to be basic difficulty. Kind of problematic if you ask me.
    - Bai: This is also very open to interpretation, I could also argue that some of the work are in fact "optimizing" something in a certain way. Does that also qualify as advanced work? We probably should request for clarification on this.
- Bai: Now what open source licenses are acceptable in this year's OSPP?
    - Liu: It says [here on the website](https://summer-ospp.ac.cn/help/#_4) that any license that is approved by OSI should be acceptable.
    - Bai: Hmm. Kind of confusing, since [here says](https://summer-ospp.ac.cn/help/community/#_10): "The following open licenses are acceptable in this event".
    - Liu: They are not specific to software-based projects, is it? It says [right here](https://summer-ospp.ac.cn/help/community/#_8) that "any license approved by OSI should be acceptable for (software) development projects".
    - Bai: I see. Still a bit confusing here.
- Bai: Okay. Now let's collect what projects we have.
    - Liu: So I will propose packages website rewrite project, and you have localization project. Do you want to re-spin the LoongArch port from last year?
    - Bai: Hmm. Consider one mentor one project limitation, someone else needs to take this project. Will you consider it, Saki?
    - Saki: I probably would not have time to mentor the project, but I will try.
    - Bai: In which case, we can also assist you when needed.
    - Liu: Have you contacted Icenowy?
    - Bai: Yes, she will mentor Allwinner D1 U-Boot mainlining project.
- Liu: So what will we do with mentors' financial reward?
    - Saki: We can upgrade hardware.
    - Bai: Yeah. Although we can't directly accept monetary reward, we can however upgrade our infrastructures.
    - Liu: Well, the reward this time got a considerable cut ... I wonder what could this amount of money do?
    - Saki: We could still buy RAM sticks.
- Bai: Are we finished collecting projects? Do other members still have other projects that yet to propose?
    - Liu: I guess that's all the projects we could have.
- Bai: Let's submit the organization request then.
    - \*\*Bai submitted organization request\*\*
    - Bai: Okay, that's everything we need to do for now. Let's meet again next week regarding finer details.
    - Liu: Alright. Let's collect project information afterwards.

\[Note: Scrapped agenda: workflow adjustments and discussions\]

Actionables
===========
- Liu: Collect project information.
- Bai: Await organization request review and submit further information.
