+++
title = "Contributor Minutes on 2023-09-23"
description = ""
date = 2023-09-23
[taxonomies]
tags = ["minutes"]
+++

Topics
======

Today's meeting focuses on OSPP 2023 Project progression, Bash Config compatibility with `sudo su`, and Coffee Break review meeting planning.

- [Omakase 1.1 Review](#omakase-1-1-review)
- [Autobuild3 Testing Framework Review](#autobuild3-testing-framework-review)

Omakase 1.1 Review
==================

- `oma remove` needs to handle residual-config status (`deinstall ok config-files`), and allow `oma purge` to remove the configuration files.
- Missing localisation in `oma history`.
- Incorrect "DUE TO" logic - this should mark the reason why an error occurred.
    - In current cases, use "INFO."

```
    ERROR Package contents database (Contents) does not exist.
   DUE TO Use the `oma refresh' command to refresh the contents database.
    ERROR jd: command not found.
```

- `oma download` in a directory with no write permission reports incorrect error, currently reports "no checksum," should report "no permission."
- Incorrect line wrapping for long prompts for English interface.
    - Prompt "If this is not an expected behavior, please enable debug mode (by adding the `--debug' flag) to record detailed program output, and submit it along with your bug report at https://github.com/AOSC-Dev/oma" is too long, break up the lines.
    - If too difficult to implement word break in the current prompt implementation, consider breaking up the lines in the translation file.
- Release planning: Save this for after 1.1.
    - Keep backups of configuration files for `oma undo` (probably in tarballs in set places, marking by package names, versions, checksums, etc.).
    - All `indicatif` strings and elements needs to be marked for justification.
- Saki to open a topic for Omakase 1.1, release go/no-go meeting next weekend.

Autobuild3 Testing Framework Review
===================================

- Leedagee demo'd the testing framework implementation and explained the implementation details - to be assembled into user/developer documentation with suggestions from attendees.
- Before October.
    - Complete documentation and submit as a pull request to [Wiki](https://github.com/AOSC-Dev/wiki).
    - Open a preview-only topic for Autobuild3 testing framework in the [ABBS Tree](https://github.com/AOSC-Dev/aosc-os-abbs).
    - Finish and submit to mentor (Camber) end-of-project report.