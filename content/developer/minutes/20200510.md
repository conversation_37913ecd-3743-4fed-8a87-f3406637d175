+++
title = "Contributor Minutes on 2020-05-10"
description = ""
date = 2020-05-10T04:19:28.094Z
[taxonomies]
tags = ["dev-automation", "dev-sys", "minutes"]
+++

OSPP 2020
---------

- Yhi: RFC on proposals and enrollment information.
    - Projects: AOINB, L10n, DeployKit.
    - Time: July to October, time considerations.
    - Starting June, receiving proposal and responding to inquiries.
- <PERSON>: I will take L10n.
    - Who will take automation?
- <PERSON>hi: Part of the compensation goes to the community.
    - Bai: How do we deal with the income/compensation?
        - No problem if the student is compensated.
        - Should we constructively respond (set up a mechanism to deal with this compensation), or should we forfeit our compensation?
    - <PERSON>hi: Can't decide.
    - Liushuyu: Makes no difference.
- <PERSON><PERSON>: I think we should constructively respond. Could use compensation for community maintenance and AOSCC... AArch64?
    - Bai: AOSCC sounds like a good place to spend the money, but who would manage this?
- <PERSON><PERSON>yu: Try SPI.
    - Bai: Geographic alignment is not of interest for the community.
- Bai: Is our community worthy to join in?
    - Yhi: We are eligible according to their information.
- Yhi: Project listings incomplete.
    - Bai: Will finish L10n, will need to hear back from Lion and Gumblex on AOINB.
- Yhi: <PERSON><PERSON><PERSON> heard that from GSoC, communities were not compensated. Will get in touch with organisers.
    - *Revised Wiki from suggestions.*
    - Will submit enrollment information, project listings idealy to be completed by May 14.

On Loongson
-----------

- Bai: Yhi, what do you think about `arch/loongson2.sh`? MMI, EXT, etc. are reserved for Loongson 3, correct?
    - Yhi: Indeed.
    - Yhi: Add `-Wa,-mfix-loongson2f-jump,-mfix-loongson2f-nop`.
- Yhi: Should check with FlyGoat, whether to use `-march=mips3` or `-march=loongson2e`. We shouldn't expect much though.
    - FlyGoat, Sun Haiyong (from out of meeting): Only `-march=mips3` could be compatible between 2E/F, 2E/F has different MMI implementations.
    - Bai: Let's use `-march=mips3`.
- Bai: Should we leave 2E alone or collect a list of MMI-aware packages?
    - Yhi: According to FlyGoat, we should just go with `-march=mips3`, and overlay `pixman` and `ffmpeg`, these are the only two known ones to use MMI (for a ~10% gain).

Debootstrap
-----------

- Liushuyu: Almost done. `groupadd` segfaults for some reason (stage 2), stage 1 (or `stub` in AOSC speech) is done.
- Bai: Can we backup stage 1?
    - Liushuyu: Yes, by null-ing stage 2.
- Bai: Issues to be fixed (deps, file location, etc.).

Minutes
-------

- Bai: Let's change Minutes titles to "Contributor Minutes on $DATE".