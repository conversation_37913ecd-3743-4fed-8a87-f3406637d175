+++
title = "Contributor Minutes on 2023-01-29"
description = ""
date = 2023-01-29
[taxonomies]
tags = ["minutes"]
+++

Attendees
---------

- Calcite
- Canary
- chenx97
- <PERSON><PERSON>
- <PERSON><PERSON><PERSON>
- <PERSON><PERSON><PERSON> (<PERSON>)
- <PERSON><PERSON> (Sa<PERSON>)
- <PERSON><PERSON><PERSON> (<PERSON>)

Agenda
======

- [CIP Work Report](#cip-work-report)
- [Hunter and aosc-archive](#hunter-and-aosc-archive)
- [AOSCC 2023 - Preliminary Planning](#aoscc-2023-preliminary-planning)
- [GSoC 2023 Application](#gsoc-2023-application)
- [AMD64 Build Server Processor Change](#amd64-build-server-processor-change)

CIP Work Report
===============

- Bai: We have completed rebuilding the `mips64r6el` against the stable repository. During the rebuild, we discovered a few more build issues, a [rework pull request](https://github.com/AOSC-Dev/aosc-os-abbs/pull/4390) has been opened.
- Bai: In the future, our community repository will host R6 packages and system releases, whereas bleeding edge packages will continue to be hosted on the CIP server.

<PERSON> and aosc-archive
=======================

- <PERSON>: I have now implemented a SQLite backend for `aosc-archive`, so that we can expose a record for retired packages.
- <PERSON>: However, I do have a question, should we store all records in one cumulative database, or create one for each round of retirement?
    - Liu: If we do split the database for each round, it might make querying difficult, so I’d prefer having a cumulative database.
    - Cyan: Or maybe serialize all entries in a single database?
        - Liu: That won’t be necessary, as the database will also record retirement time for each package.
    - Cyan: How might Hunter make use of this database?
        - Liu: Hunter will not directly query the database. `aosc-archive` will output a data file (in CSV or JSON) for Hunter.
    - Saki: This sounds fine, but would the database (or data file) get too large?
        - Liu: These files are really compression-friendly so it shouldn’t be a problem.
            - Saki: Okay.
        - Liu: Though the file will get large eventually.
            - Saki: It’s probably fine, we can just rotate the data file in set intervals.
- Saki: Well, my original idea was to also list user-installed packages, but since we’ve discussed this and reached a consensus against this idea, I’d just leave this out.
    - Liu: It wouldn’t be difficult to implement, we only need to list packages that are not in the repository or on the retirement records. Additionally, the retirement database also records the package versions of those retired, this can help improve accuracy, too.
    - Bai: This can be output as a “reminder” but not a warning.
- Bai: So, how long until Hunter is ready?
    - Liu: I can probably finish the `aosc-archive` side tomorrow, if not, it will have to wait until the next weekend. I still need to implement two arguments for the tool to specify database and data file paths (for Hunter), as well as adding xz compression support.
    - Saki: After `aosc-archive` is ready, I can easily implement Hunter within a week.

AOSCC 2023 - Preliminary Planning
=================================

- Bai: Let’s tentatively plan for location and date for this year’s AOSCC, which will hopefully be held in-person.
    - Liu: I think July should be perfect, since we want to let students attend during their summer vacations. It should also be held on a weekend, as a lot of us are now working full-time.
- Bai: Canary offered a possible lead for the venue.
    - Canary: Yes, ShanghaiTech University can probably provide a venue, which is right next door to where AOSCC 2016 was held.
- Bai: Any specifications and requirements for dates?
    - Canary: Probably not, I only need to notify the school of an open event and communicate with the executive office. Summer vacation is probably fine. In case the executive office couldn’t help us, I can ask a professor to clear us through.
    - Liu: When we held AOSCC 2019 at the University of Science and Technology of China, despite having reference letters in hand, the security guards still gave us trouble.
        - Canary: Again, I can ask a professor to clear us through. We can also make use of the RISC-V Summit that will be held at ShanghaiTech this summer.
            - Bai: I don’t think it would be ideal to have two conferences collide with time.
                - Cyan: Seconded.
    - Canary: Here’s an [academic calendar](https://www.shanghaitech.edu.cn/eng/2022/0915/c1291a839560/page.htm ), for your reference. At any rate, I will need to wait until the executive office returns to work from the Spring Festival. I think it should be easy, since the university does favor more open events.
- Canary: One last question, in case July doesn’t work, would August or September be okay?
    - Bai: Let’s aim for July and August to make it easy for most people.
- Icenowy: The PLCT Lab also offered to donate 10,000 CNY to help us fund the event and venue. They also mentioned that the CNRV conference will be held in Guangzhou on July 23 this year, which is also a possibility for a shared venue.
    - Icenowy: Otherwise, if we want to avoid time conflicts, we can request the event hosts to rent the venue for a few more days so that we can make use of it too.
    - Icenowy: Though at any rate, I think ShanghaiTech is still our best bet.

GSoC 2023 Application
=====================

- Everyone: Submitted application and [drafted potential projects](https://wiki.aosc.io/community/gsoc/2023-projects/).

AMD64 Build Server Processor Change
===================================

- Lily: Since we have extra funds from the crowdsourcing project, we have decided to go for an AMD EPYC 7H12 (64 cores)  instead of a 7R32 (48 cores). However, my supplier quoted this processor at 12,000 CNY, so we are still short. The supplier also offered a lower-cost alternative, 7V12.
    - Bai: Well, it seems to have lower frequencies.
    - Saki: Single-threaded performance would be terrible.
- Bai: I think we’d rather take the 7R32 over 7V12, if it comes to that. Weren’t there cheaper 7H12 on Xianyu?
    - Lily: Yes, but I do worry about their reliability. The supplier I’ve mentioned above offers a five-year warranty.
        - Bai: Okay, let’s communicate for a better price and make sure that we can get refunds or replacements in case of DoA.
- Liu: I can cover the gap.

