+++
title = "Contributor Minutes on 2020-07-05"
description = ""
date = 2020-07-05T04:15:14.448Z
[taxonomies]
tags = ["minutes"]
+++

Core 8
------

- <PERSON>: How should we go about doing this? Where and when? We used Telegram in 2018, is this the best we could do?
    - <PERSON>: Nominations on Monday through Friday, and vote on Friday?
    - <PERSON> X.: 3 - 5 business days?
    - <PERSON>: Let's use calendar days...
    - <PERSON>: Starting next Monday?
    - <PERSON>: Starting on Wednesday night?
    - <PERSON>: So that they have some time to think about it?
    - <PERSON>: Let's start collecting nominations and vote on Wednesday night?
    - <PERSON>: Sounds good to me.
- <PERSON>: Vote on multiple rounds?
    - <PERSON>: Sure, let's do it like how we did before. So that we can eliminate some.
- <PERSON>: How long should we vote last?
    - <PERSON>.: Until the weekends?
    - Lion: One or two days should suffice.
    - <PERSON>: One or two days should be enough, I think.
    - <PERSON>: I think a rapid fire should do.
    - Lion: Timzezone is an issue.
    - Neo: 48 hours?
    - Bai: 48 total or per round?
    - <PERSON>: I second that.
- <PERSON>: We should come up with a schedule since it's multi-round.
    - <PERSON>: That's right... It's easy to get ties on the first round.
    - <PERSON>: Let's do 12 hours per round?
    - <PERSON>, <PERSON>: Sure.
    - <PERSON>: That sounds good to me, 12 or 24 works well.
- <PERSON>: Which platform should we use? Telegram only seems to support single choice.
    - <PERSON>, <PERSON>: Telegram should do just fine, it does support multiple coice.
    - <PERSON>: Oh that's fine then.
- <PERSON>: Like how we did it before, no politics, no profanity, and no trademarks?
    - <PERSON> X.: Let's do a quick audition before hand?
    - <PERSON>: That sounds good, since we have two days before the vote.
    - Lion: And sexual hints.
- <PERSON>: How should we divide the 12 hours? It could be difficult for people to keep track of it.
    - Bai: Should we do 12 hours consecutive or?
    - Lion: Let's do 12 hours same time everyday.
    - Bai: 10 A.M. UTC +8 daily?
    - Liu: Sounds good.
    - Lion: Actually let's do 24-hour rounds, ending 20 minutes early to prepare for the next round.
    - Bai: Sounds good, so we will begin voting on Thursday 10 A.M., UTC+8, repeating on Friday and Saturday until we have a winner.
- Bai: How should we collect nominations?
    - Liu: I have an idea... Google Forms?
    - Neo: That's fine...?
    - Bai: Or GitHub issue? But both options sound good.
    - Lion: I second GitHub issue.
    - Liu: Sounds good.
    - Bai: GitHub issue it is then.

DeployKit
---------

- Bai: Liu, could you demonstrate DeploytKit?
    - Liu: Give me a moment to set up a VM.
- *Liu demonstrates his work-in-progress DeployKit (TUI)...*
- Bai: BuildKit should not be listed here since it's not bootable.
    - Liu: They are displayed as-is from the manifests downloaded.
    - Bai: Let's just add an filter.
    - Liu: Sounds good.
- Liu: Another issue is that many of our mirrors do not provide system distribution downloads (they are huge).
- Liu: "No partition selected" is displayed when none is detected.
    - Bai: Please change it to "No partition defined."
- Bai: I think all windows are too narrow, they need some padding around them.
- Liu: Partitioning step defines whether X11 is detected - if detected, it gives an option to launch GParted, otherwise, it launches a shell with a prompt.
- Neo: More customisation options for users?
    - Liu: Leave them to unpack tarballs themselves, there's no point to guide them since they probably knew better...
- Neo: Should we use 3D buttons like old Red Hat versions do?
    - Liu: That may take too much space.

OSPP 2020
---------

- *Discussion cancelled as most OSPP mentors were absent.*

Actionables
-----------

- Bai:
    - Create an issue on GitHub to collect nomination.
    - Complete Spring Cycle in the next few days.
- Liu: Continue implementing DeployKit.