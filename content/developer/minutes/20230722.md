+++
title = "Contributor Minutes on 2023-07-22"
description = ""
date = 2023-07-22
[taxonomies]
tags = ["minutes"]
+++

Agenda
======

- [Review: AOSCC 2023 Recap](#review-aoscc-2023-recap)
- [AOSCC 2023 Survey Results and Report](#aoscc-2023-survey-results-and-report)
- [CIP Communication: MIPS32 R6 and Automation Instrumentation](#cip-communication-mips32-r6-and-automation-instrumentation)
- [AOSC OS "Devena"](#aosc-os-devena)
- [ABBS Tree Security Report Template](#abbs-tree-security-report-template)

Review: AOSCC 2023 Recap
========================

- Reviewed news drafts and readied for posting.
    - Posted on [Community Portal](https://aosc.io/news/posts/2023-07-22-aoscc-2023-re-cap/).
- ShanghaiTech's SIST will post a WeChat newspost on AOSCC 2023 (nominally "GeekPie Workshop").

AOSCC 2023 Survey Results and Report
====================================

Mingcong Bai to assemble an RFC report by July 23rd, to announce community-wide after revision.

CIP Communication: MIPS32 R6 and Automation Instrumentation
===========================================================

- MIPS32 R6 to remain a Proof-of-Concept port, and will remain in the [AOSC-CIP-Pilot/aosc-os-abbs](https://github.com/AOSC-CIP-Pilot/aosc-os-abbs) tree.
- liushuyu to review [Ciel](https://github.com/AOSC-Dev/ciel-rs) and [aosc-findupdate](https://github.com/AOSC-Dev/aosc-findupdate) changes soon.
    - CIP employees may use patched tools should liushuyu's time availability not allow.

AOSC OS "Devena"
================

- AOSC OS "Devena," DevEna, Device Enablement.
- What should it cover?
    - Forked kernels.
    - Specialized system services, scripts, bootloader config templates (those that does not support multiple concurrently installed kernels).
    - Those suitable for mainline/official kernels may be pushed to the main tree.
    - EBBR (for devices with official kernel support and U-Boot support - RPi 3/4, H6, RK3399, etc.).
- How should it exist?
    - Expands upon the current [BSP tree](https://github.com/AOSC-Dev/aosc-os-bsps).
    - Standardizes system release generation, simplified installation processes (image builder, customized system installers).
- On Git, use multiple repositories (tentatively prefixed with "aosc-os-devices") for...
    - Make use of existing bsp, rawimg, etc (e.g., [aosc-mkrawimg](https://github.com/AOSC-Dev/aosc-mkrawimg)) infrastructures.
    - Also: Customized installation media, specialized bootloader configs, etc.
- Primary targets for trial? Not clear yet but plans to be published soon.
    - Each device should have a set maintainer.
    - Per-request support, requestor assigned as tester.
- Where and how should it be released?
    - Release only Base releases for most devices.
    - Some devices with known Desktop capabilities will have Desktop releases.

ABBS Tree Security Report Template
==================================

- Make sure that only one package is listed in a single report.
- Mark "IDs" => "ID(s)."
- Do not ask submitter to analyze impact to AOSC OS.
- "Original mailing list message(s) or other description may be listed here." => "List of links to reference."
- Add a short description on 0-day vulnerabilities.

Under Review: [#4601](https://github.com/AOSC-Dev/aosc-os-abbs/pull/4601)
