+++
title = "Contributor Minutes on 2021-03-29"
description = ""
date = 2021-03-29
[taxonomies]
tags = ["minutes"]
+++

Contributor Minutes on 2021-03-29
=================================

Attendees
---------

- Chenx97
- Cth451
- Cyanoxygen
- Fear<PERSON><PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- RedL0tus
- Zixing Liu

Agenda
------

- [Changes to the Topic-based Iteration System](#)

Minutes
-------

# Changes to the Topic-based Iteration System

- Bai: So I will present some of my suggested changes to the current iteration system,
and I would also like to hear from you as well.
    - Bai: One of the main focus I have is that, after the build tests and usability tests
    have passed, instead of merging, a label is applied to pull request. Then after all the
    packages for the Tier-1 architectures have been pushed to the stable branch, we then merge
    the pull request. For Tier-2 architectures, they don't affect the merging process just like before.
    - Bai: That being said, if you have the time and resources to build the packages for the Tier-2 
    architectures, you can of course do that.
    - Bai: This is to prevent the "rework" process on the Tier-1 architectures, as this process does not 
    require any testing.
    - <PERSON>: I also think fully testing everything before merging is better.
    - <PERSON>: We don't have testing requirements for Tier-2 architectures, so rework process on Tier-2 
    architectures is fine. However, inconsistencies may happen when applying rework to Tier-1 architectures.
    - Mag Mell: However, I still think it is not possible to avoid the rework process. Since you may find other issues down the line.
    - Bai: What you just said is not a problem for a rework, rather, a new "fixing" topic. If any further 
    issues are encountered, they don't belong to a rework process.
- Bai: Also, in what condition an error like that should arise?
    - Leo: For example, linkage errors found by the `p-vector`.
    - Bai: I see. Besides, `autobuild` has partially gained the abilities to detect such errors.
    - Bai: `autobuild` also has a function called `elf_deps`, which will automatically populate the required 
    dependencies. The issue with this function is that, `PKGRECOM` would complicate things.
    - Bai: The rework process is for the packages that, after merging, it somehow could not be built successfully.
    - Cth451: Usually the cause is that the changes during a `rebase` action is not taking into account.
    - Bai: So we need to enforce a rule where "rebase" on the GitHub website is forbidden.
    - [.. Discussion regarding how the rule should be enforced omitted ..]
    - Leo: Actually this changed the meaning of "Approved" from "the build scripts look good" to "it's good to be merged".
- Bai: Is there anything else that could be improved or changed?
    - [.. discussions regarding testing on loongson and purchasing devices omitted ..]
    - [.. discussions regarding RISC-V omitted ..]
    - Cyan: Can the process be simplified for things like `youtube-dl` and other packages with a high update frequency? Like skipping all the tests and just upload them after packaging.
    - Bai: If you just want to be fast, our topic-based system would be meaningless. However, I do encourage
    everyone to review others' pull requests to speed up the reviewing process. 
    Keep waiting for me to review is wasting your time.
    - Bai: Although, we could consider the possibility of simplifying the process for packages that do not
    require actually building [note: producing binaries] and the testing is not necessary.
    - Cth451: I think in which case, all the `noarch` packages should be considered to be in this category.
    - Mag Mell: Objection! A counter example is one of the RIME data packages, which is `noarch` but requires 
    building.
    - Bai: I would exercise some caution here though. I think we should start from the pure data packages, where the building process is just copying files, like fonts and themes.
    - Bai: However, how do we handle the pull requests that needs simplified process? Using an exception list like before?
    - Liu: Add a new label could help as well.
    - Leo: Like "trivial pack"?
    - Bai: Even "rushed" would work, it's not important at the moment.
- Leo: How do we signal that one pull request is ready to be merged?
    - Bai: Just don't use the emoji reaction. It's difficult to standardize. We can use labels.
    - Bai: Also, suggestions from Liu about using GitHub Actions to auto-label pull requests sounds promising
    as well.
- Leo: On the other subject, we need to fix our _Installation Guide_.
    - Bai: Right, also **tarballs**.

Actionables
-----------

- Zixing: Implement GitHub Actions to auto-label pull requests
