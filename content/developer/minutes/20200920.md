+++
title = "Contributor Minutes on 2020-09-20"
description = ""
date = 2020-09-20T23:03:55.386Z
[taxonomies]
tags = ["minutes"]
+++

Contributor Minutes on 2020-09-20
---------------------------------

Attendees
---------

- Chenx97
- <PERSON><PERSON>wy Zheng
- <PERSON> Bai
- Staph
- Zixing Liu

Agenda
------

- [AOSCC 2020 Check-in and Assignments](#aoscc-2020-check-in-and-assignments)
- [Shipping New Wiki?](#shipping-new-wiki)

Minutes
-------

### AOSCC 2020 Check-in and Assignments

- Bai: What do you think of our time schedule? Especially for those of you in North America.
    - <PERSON>, St<PERSON>: Works for me.
- Bai: Is there any other topic that we should talk about or touch on during AOSCC?
    - <PERSON>: Nothing that I could think of.
    - Icenowy: Trimming dependencies for easier bring-up when building new ports, and clean up our base-* and extra-* categories? These are both messy issues that we should address.
        - Bai: Let's wait until topic-base.
        - <PERSON>: We can talk about these later since they are really material for our routine meetings.
- <PERSON>: Speakers, please have your materials ready by next Thursday.
    - <PERSON>, <PERSON><PERSON>: Sounds good.
    - Bai: (To <PERSON>nowy) You might not need one since it's really a discussion, but maybe have a rough plan ready before going in?
        - Icenowy: Okay.

### Shipping New Wiki?

- Leo: Where should we be hosting Wiki in the future?
    - Bai: In terms of server?
    - Leo: It's currently under my Netlify server.
    - Bai: Just use our current Wiki.js server? Who is currently maintaining it?
        - Liu: It's on the Portal server.
        - Bai: Sounds good, let's move it over.
        - Leo: It will then require a CI, or `cron zola`?
        - Bai: Systemd timer would also work.
        - Leo: It's a bit ugly.
        - Bai: Not a big deal I'd imagine?
        - Liu: We can also use our Portal mechanism, building pages following Git Pushes and uploading them to the server.
        - Leo: Sounds good. Since cronjobs may run "dry."
- Chenx97: We should also investigate missing pages on the Wiki.
    - Liu: That might be difficult.
    - Leo: Let's put them under the Meta section.
- Bai: Did we finish cleaning up 404 and bad links?
    - Liu: We are running into an issue where Wiki.js exports bad pages, and it doesn't notify us about missing pages.
    - Bai: What then should we do?
            - Liu: We can't.
            - Bai: That's fine, just leave them out since no one's complaining yet.
            - Liu: I can still dump the database and see how we go.
- Leo: We may also need to rewrite the homepage. Dropping "under construction" markers?
    - Bai: We can remove the marker once all 404 are fixed.
    - Chenx97: This may be difficult since Zola's checker triggers rate limits on GitHub.
    - Liu: Just use a rate limiter.
    - Leo: We can just put a "work list" under Meta.
        - Bai: Let's get everything sorted before we ship.

Actionables
-----------

- AOSCC 2020 Speakers: Prepare material and have them ready by next Thursday.
- Bai: Re-write Wiki homepage before shipping.
- Chenx97, Leo, Liu: Continue cleaning up Wiki and rid it of 404's and missing pages.
