+++
title = "Contributor Minutes on 2023-11-19"
description = ""
date = 2023-11-19
[taxonomies]
tags = ["minutes"]
+++

Topics
======

Today's meeting focuses on various project planning and updates.

- [Omakase 1.2 Testing](#omakase-1-2-testing)
- [Omakase: Rename to oma?](#omakase-rename-to-oma)

Omakase 1.2 Testing
===================

- `oma --ailurus` does not display the Easter Egg correctly on smaller screens (< 116col).
    - Originally, there should be a 80 - 116col mode.
    - Omakase should have returned an error when terminal width is < 80col.
- `oma install --sysroot` only functions with absolute path.
- When running `oma refresh`, falsely resolved DNS addresses (such as `repo.o`) provided by ISP may cause Omakase returns a huge amount of empty lines.
    - AT&T redirects to dnserrorassis.att.net and Omakase attempted to read from the last file name on that URL.

```
Notes from cth451:

oma prints out filename component from effective URLs (may be different from one used in sources list due to server side redirects) upon download failure.

ISPs engaging in non standard behavior may redirect NXDOMAIN into URLs that are too long to print on the terminal
```


- Consider reverting d0c7e3b4d78efa8c05bb91cfbef0d0f0284892d8, which seem to have broken the progress bar layout again.
- Possible upstream bug: pkexec cancellation returns code 127 (should be 1?).
- Record failed Omakase operations with `[Failed]` (or something similar).
- `oma download` does not return permission error "DUE TO" while attempting to overwrite .deb files created by root.
    - Or for any file operations in general.

Omakase: Rename to oma?
=======================

Tabled as a trivial topic.
