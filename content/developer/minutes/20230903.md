+++
title = "Contributor Minutes on 2023-09-03"
description = ""
date = 2023-09-03
[taxonomies]
tags = ["minutes"]
+++

Agenda
======

- [OSPP 2023 Project Sync](#ospp-2023-project-sync)
- [Pull Requests for AOSC Toolchains](#pull-requests-for-aosc-toolchains)
- [Omakase 1.1 Review](#omakase-1-1-review)
- [Topic Sync](#topic-sync)
- [Mascot Sticker Set Design](#mascot-sticker-set-design)

OSPP 2023 Project Sync
======================

Camber and Leedagee
-------------------

- Been busy, not many updates since last week.
- <PERSON><PERSON><PERSON> urged to open a pull request to allow testing and feedback.

<PERSON> and <PERSON>ao
--------------

- Three new projects currently under review, Kicker- and Kate-related.
- Added [contributors' guidelines](https://l10n.vantao.cn/contribution) to the L10n site.
    - Should add more instructions on review and change proposals.

Pull Requests for AOSC Toolchains
=================================

- Ciel-rs: [#12](https://github.com/AOSC-Dev/ciel-rs/pull/12) `delall` if passed without any argument, deletes all instances.
    - Preferrably, implement `del --all`.

Omakase 1.1 Review
==================

- Add a "no-animation" switch for ACBS/Autobuild3 logs - `--no-progress`?
- Drop purge-as-default and differentiate `remove` and `purge`.
- Keep backups of configuration files for `oma undo`.
    - Where should we keep them?
- Trim the review instruction in console log after user confirms with Q, such as (only happens when using `--install-dbg`/`--yes`).
- Missing localisation for "ping-pong" throbber to `search`/`clean`.
- Tell users that the `/` and `=` syntax could not be used (maybe allow for using topics) outside of install (for instance, in `show` and `rdepends`).
- `oma fix-broken` removes dependency even when the dependency is available (APT behavior? Report to upstream?).
- Use past tense for all `history`/`undo` entries.
- `oma history`: Return to the history menu after reviewing an item and hitting Q.
- Use terminal mode for dpkg, `DEBIAN_FRONTEND=noninteractive`.

Topic Sync
==========

- GCC (`gcc` and `gcc-runtime`) 12.3.0-2 with POWER Ada support was not pushed.
    - liushuyu pushed the missing package, Core K should build now.
- After Core K, commence merging LoongArch port (`frontier`).
- OpenSSL 3.1.1 is almost ready, about 10 packages to go.
- Pinged Leedagee for [#4580](https://github.com/AOSC-Dev/aosc-os-abbs/pull/4580).
    - Pull request is now ready for review and merging.
- Pinged Camber for [#4621](https://github.com/AOSC-Dev/aosc-os-abbs/pull/4621).
    - More code clean up pending.

Mascot Sticker Set Design
=========================

Contacted illustrator, [五十根炸虾](https://www.mihuashi.com/profiles/571437), response pending.
