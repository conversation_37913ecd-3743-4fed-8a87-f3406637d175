+++
title = "Contributor Minutes on 2023-06-11"
description = ""
date = 2023-06-11
[taxonomies]
tags = ["minutes"]
+++

Agenda
======

- [AOSCC 2023](#aoscc-2023)
- [OSPP 2023](#ospp-2023)
- [Website "Odyssey" Review](#website-odyssey-review)
- [Omakase Review](#omakase-review)

AOSCC 2023
==========

- We had to replace ThinkPad T43 with X41 due to market availability.
- Give <PERSON> Wei 30 minutes, move PLCT to a featured presentation (14:00 - 14:30), push lightning talks back.
- Canonical lightning talk (<PERSON><PERSON><PERSON> <PERSON>) needs clearance from employers.
    - Lifestyle and work experiences does not require clearance, topics on product do.
- To confirm Geek<PERSON>ie speaker and topic, SHLUG needs topic.
    - GeekPie: 张亦驰, "展示 GeekPie 新服务架构."
- [Registration form](https://forms.gle/SNwrdLVabf8TDcTh7) (those who registers after July 13 will need special clearance).
- [T-shirt order form](https://forms.gle/2Qhjh4LAMvzbmaFf7).

OSPP 2023
=========

- DeployKit GUI has no examination submission, project forfeited.
- Notified students for Autobuild3 Testing Framework and Localisation projects.

Website "Odyssey" Review
========================

- Figure out a standard color pallette for seasonal/locale-specific changes.
- Possible primary/secondary highlight colors to separate main navigation with secondary ones.
- List news summaries on the main page.
- Implementation.
    - Prioritise modern Web browsers.
    - Make Retro/Mobile-specific websites with reduced content.

Omakase Review
==============

- Do not list no-op commands into history (e.g., `oma upgrade` with no available update).
- "Read interrupted" error if Ctrl-C at the `oma history` menu view.
- Move `undo` and `redo` to top level?
    - No, not a frequent subcommand.
- Add a note that `undo`/`redo` will not rollback deleted/replaced configuration files.
- Uneven progress bar lengths when elipses are used.
- Limit maximal width of the UI (to 150 columns?).
    - Still needs a width limit for indicatif elements.
- Omakase segmentation faults with frequent Ctrl-C (fault in signal handler).
- Time to merge into admin-base, it’s close enough.
    - Keep apt commands around for a good amount of time.
    - Default to oma command instructions in documentation.
    - Wait for localization.
