+++
title = "Contributor Minutes on 2024-01-25"
description = ""
date = 2024-01-25
[taxonomies]
tags = ["minutes"]
+++

Spiral 实现
===

简述
===

Spiral 是一个通过自动探测软件包内容，按 Debian 软件包的命名规范自动标记 Provides，以期实现 Debian/Ubuntu 软件包兼容性的框架

规范
===

- 实现可以掌握的规律即可，其余的按照需要手动标记
- 可以直接转译的，如 `gtk-3: libgtk-3.so.1 => libgtk-3-1 + libgtk-3-dev`
    - 一般规则：`soname` + `-` + `sover`；开发包 (`-dev`)：`soname` + `dev`
    - 如有 `soname`，则 `lib` 文件名和 `soname` 都按照规范转译
    - 需排除静态库
    - 不规范的部分，`PKGPROV=` 内容作为补充
    - 亦可实现 Autobuild 内置宏排除和替换 Provides 项目
- 考虑到 Python 和 Perl 等包包名规范不统一，非 ELF 包的 Provides 均手动标记
- 只需标记 `/usr/lib` 下的文件，而不需要标记更深层的文件
- Provides 版本按当前包的版本标记 `=`，如果遇到和 Debian 不相等的 Epoch，则要求手动标记

实现计划
===

- 在 Autobuild4 的 `abnativeelf` 模块下实现
    - 使用 `ABSPIRAL` 作为开关名
    - 默认打开该功能
- oma 需要实现多软件包 Provides 的用户选择逻辑

工作计划
===

- 咸鱼：尽快在 Autobuild4 内实现相关功能
- AOSC OS：短期内，先按照 Electron 等应用的热点包重构，后续全系统重构

其他意见
===

王邈建议：

- Provide 的 Epoch 一律设置为 0（考虑到 Debian 上一般不轻易提高 Epoch）
- 如果需要改 Epoch，可以用单独环境变量指出

参考材料
===

- [现有 Spiral 实现](https://github.com/spiral-repo/spiral-rs/blob/master/spiral/src/translate.rs)
- [Debian 软件包名规范](https://www.debian.org/doc/debian-policy/ch-controlfields.html)
