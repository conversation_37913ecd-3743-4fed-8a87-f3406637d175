+++
title = "Contributor Minutes on 2023-12-02"
description = ""
date = 2023-12-02
[taxonomies]
tags = ["minutes"]
+++

Topics
======

Today's meeting focuses on various project planning and updates.

- [Review: Raspberry Pi OS First-boot Experience](#review-raspberry-pi-os-first-boot-experience)
- [Review: Armbian First-boot Experience](#review-armbian-first-boot-experience)

Review: Raspberry Pi OS First-boot Experience
============================================

- GUI (https://github.com/raspberrypi-ui/piwiz).
- Automatically pairs bluetooth mouse and keyboard (put devices into pairing mode).
- Default browser selection.
- Update software on first-boot (but it was shown even when network connection isn't available).

Review: Armbian First-boot Experience
=====================================

- Command-line prompt wizard.
- "Desktop environment will not be enabled if you abort the new user creation."
- Timezone organised by "Beijing" and "Xinjiang" timezone - this may be a little more familiar than Shanghai, Chongqing, etc.
