+++
title = "Contributor Minutes on 2023-07-29"
description = ""
date = 2023-07-29
[taxonomies]
tags = ["minutes"]
+++

Agenda
======

- [AOSC OS Core 11 Components and Scheduling](#aosc-os-core-11-components-and-scheduling)
- [Outreach Workgroup for Bilibili, WeChat, etc.](#outreach-workgroup-for-bilibili-wechat-etc)
- [Distro Review: openEuler Community Edition 22.03 LTS SP2](#distro-review-openeuler-community-edition-22-03-lts-sp2)
- [Distro Review: RHEL Workstation 9.2](#distro-review-rhel-workstation-9-2)

AOSC OS Core 11 Components and Scheduling
=========================================

- While updating GCC, keep a note that Binutils must be bumped.
- RISC-V rebuilds after GCC 13 update.
    - Compiler tooling improvements results in faster binaries being generated.
    - Replacing system components step by step, instead of a full system rebuild?
- This topic must not be merged before LLVM 16.
    - LLVM 15 will not build with GCC 13.
    - There are remaining GCC 12 changes, including D language enablement for POWER, which will aid GCC 13 bootstrapping.

Outreach Workgroup for Bilibili, WeChat, etc.
=============================================

- Set e-mail for Bilibili, will need phone authentication.
    - Mingcong Bai is the current email and phone number owner.
    - To be decided: What to do when Mingcong Bai returns to the United States.
- Opened an official WeChat account (ID: aosc_io), currently under audit.
- Probably need a new Weibo account?

Distro Review: openEuler Community Edition 22.03 LTS SP2
========================================================

See our [survey notes on openEuler Community Edition](/developer/notes/distro-survey-2023#openeuler-community-edition).

Distro Review: RHEL Workstation 9.2
===================================

See our [survey notes on RHEL Workstation](/developer/notes/distro-survey-2023#rhel-workstation).
