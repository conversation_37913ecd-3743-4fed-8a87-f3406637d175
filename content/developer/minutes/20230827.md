+++
title = "Contributor Minutes on 2023-08-27"
description = ""
date = 2023-08-27
[taxonomies]
tags = ["minutes"]
+++

Agenda
======

- [OSPP 2023 Project Sync](#ospp-2023-project-sync)
- [Omakase 1.1 Review](#omakase-1-1-review)
- [Topic Sync](#topic-sync)
- [Sticker Pack for An-An and Tong-Tong](#sticker-pack-for-an-an-and-tong-tong)
- [Distro Review: Solus Budgie 4.4](#distro-review-solus-budgie-4-4)

OSPP 2023 Project Sync
======================

L10n: <PERSON><PERSON> (Mentor: Mingcong Bai)
---------------------------------------

- L10n Cook Book.
    - Consider listing all authors in the "Maintainers" section.
    - Use author names (in et. al. form) in the copyright footer.
- Move on to tdebase/kcm* as the next review and translate targets.

Omakase 1.1 Review
==================

- Revise remove/purge behaviours.
    - Keep purge-as-default.
    - Do not replace old configuration on `oma install --reinstall`.
    - If `--reinstall` is specified, ask users how to handle old configuration.
- "Detail" at the `remove` confirmation table should be left-justified.
- Trim the review instruction in console log after user confirms with Q, such as...

```
Shown below is an overview of the pending changes Omakase will apply to your
system, please review them carefully.

Omakase may upgrade, downgrade, or reinstall packages in order
to fulfill your requested changes.
```

- Operation details were printed twice when using `oma install --yes/--install-dbg`.
- Removing `zsh` did not remove `zsh-dbg` and no changes applied (no error returned).
- Path not printed when `--path` is specified for `oma download`.
- Add `--all` full form for `-a` parameters.
- Add "ping-pong" throbber to `oma search/clean`.
- Tell users that the `/` and `=` syntax could not be used (maybe allow for using topics) outside of `install` (for instance, in `show` and `rdepends).
- `oma fix-broken` removes dependency even when the dependency is available (APT behavior? Report to upstream?).
- `oma history` interface.
    - Exclude `fix-broken` operations from `history`.
    - Clarify that the interface is used to replay a previous operation.
    - Return to the history menu after reviewing an item and hitting Q.
- Terminal behavior - `oma install fish 2>&1 > log` failed to redirect output to file, nor did piping to `less`/`tee` trigger terminal behaviour.
    - Extraneous ^G at the end of the log.
    - Use terminal mode for dpkg, DEBIAN_FRONTEND=noninteractive.

Questions
---------

- Add an option to list only the rdepends of the newest version of the package specified?
- Keep q as confirmation for oma topics, use / to search?

Topic Sync
==========

- Cherry-pick Node.js 18.x update out of `frontier` to a separate topic, as 16.x will EOL in September.

Sticker Pack for An-An and Tong-Tong
====================================

- Saki to contact illustrator [五十根炸虾](https://www.mihuashi.com/profiles/571437?role=painter).
    - Ask for a sample (pay the illustrator if needed) that is derived from our original mascot design.
    - Further designs to be paid through crowdsourcing.

Distro Review: Some Notes on Procedure
======================================

- We are not to publish notes in our outreaching materials to prevent excessive exposure.
- All "Anti-feature(s), flaw(s), and bad decision(s)" items (marked by ×) must be reported to the upstream when possible.

Distro Review: Solus Budgie 4.4
===============================

See our [survey notes on Solus Budgie](/developer/notes/distro-survey-2023#solus-budgie).
