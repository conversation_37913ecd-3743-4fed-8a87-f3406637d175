+++
title = "Contributor Minutes on 2023-04-15"
description = ""
date = 2023-04-15
[taxonomies]
tags = ["minutes"]
+++

Agenda
======

- [LiveKit Review](#livekit-review)

LiveKit Review
==============

- Remove "AOSC OS:" from the GRUB menu entrie names.
- Move the "to RAM" option to advanced options.
- Add options for "test media" or "RAM test."
- Tofu on GRUB menu instructions (bad fonts?).
- Force 1024x768 on "command line only" mode.
- Add a multilingual menu for GRUB, or add one during boot.
- Remove the menu bar from MATE Terminal (while running as root).
- DeployKit: Broken benchmark function.
- DeployKit: Select a partition with "acceptable FS" by default (instead of
  selecting the first, which is usually the ESP).
- DeployKit: Consider adding a resume function to the download routines.
- DeployKit: Full name support (write to /etc/passwd after the user is created).
