+++
title = "Contributor Minutes on 2023-03-04"
description = ""
date = 2023-03-04
[taxonomies]
tags = ["minutes"]
+++

Agenda
======

- [Omakase Review](#omakase-review)
- [Desktop Review](#desktop-review)

Omakase Review
==============

- Main progress bar not colored correctly when launching `oma` with a smaller
  terminal window.
- `oma list/show` does not show all versions and does not prompt users about
  relevant parameters.
- `oma download` does not prompt download completion.
- `oma install` does not request root permissions.
- Lacks a `clean` subcommand to remove downloaded and installed packages.
- `oma search` results are not sorted consistently.
- `oma provides` can be slow when a lot of matches are found - consider adding
  a progress bar.
- `oma mark` lacks subcommand descriptions.
- `oma list` does not show "automatically/manually installed" statuses.
- `oma remove` should have a `purge` alias.

Desktop Review
==============

- Touchpad "tap-to-click" not enabled by default.
- Disable "?" button from title bars.
- Enable "double click window icon to close windows."
- Font installation via KNewStuff fails (no fonts are actually installed).
- Empty "background type" in "Workspace Behavior > Lock Screen."
- Limit "recent file" listing to one month.
- Enable "show desktop" option on task switcher.
- Add "Ctrl-Alt-T" shortcut to Konsole for super-key-less keyboards.
- Associate .tif/.tiff files with Gwenview (though `image/x-tiff-multipage`
  MIME type should be associated with Okular).
- Gwenview, Okular, Dolphin fails to open or generate thumbnails for a
  [large TIFF file](https://mars.nasa.gov/bin/panos/PIA23623.tif).
- Consult the upstream: use "traditional file progress dialogues" with Dolphin.
- Remove "Community Design" type from the clock widget.
- Firefox crashes when opening wheelmap.org.
- Default map to OpenStreeMap.
- Dolphin should prompt before moving files to the recycle bin.
- Move Simple Scan to the Productivity category.
- Reconsider pre-installed games.
    - 连连看 [llk-linux](https://llk-linux.sourceforge.net/).
        - Needs porting to ALSA.
        - Scaling issue, defer to Retro.
    - Frogatto.
    - Tuxemon.
    - Irrlamb.
        - Disable fullscreen by default, change default movement keys to WSAD.
    - Surge the Rabbit.
        - Disable webpage launch after game exit.
    - Cave Story NX.
    - Pigami.
    - KPatience, KMines.
- Discover crashes after installing Flatpak packages.
- PE files should be launched with Wine Application Loader by default.
