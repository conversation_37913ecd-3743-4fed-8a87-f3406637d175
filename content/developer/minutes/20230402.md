+++
title = "Contributor Minutes on 2023-04-02"
description = ""
date = 2023-04-02
[taxonomies]
tags = ["minutes"]
+++

Agenda
======

- [OSPP 2023 Sign-Up](#ospp-2023-sign-up)
- [Omakase Review](#omakase-review)
- [Pixie <PERSON>](#pixie-demo)

OSPP 2023 Sign-Up
=================

Not available for project submission yet.

Omakase Review
==============

- `oma history/log` crashes.
- Remove `-d` for `--dry-run` to avoid confusion with `--debug`.
- Only use "depended by" with `rdepends`, not `depends`.
- No need to list package names in `oma download`.
    - "Successfully downloaded package(s) to /.../..."
- Add more outputs to longer running operations (dependency resolution,
  integrity checking, etc.), along with an animation.
- List installed package in `oma search` results at the top.
- Uneven progress bar lengths depending on download unit size.
- Add `--dpkg-force-all` to 3rd retry attempt at install/upgrade, then run the
  same command again to verify (or use libapt to verify breakage).
- Limit maximal width of the UI (to 150 columns?).

Pixie Demo
==========

- Pending implementation for: dependency exclusion, Python/Perl/Ruby/PE support.
- `pscan -s` should display full soname (lib...so.N).
- Display "WARNING" by default.
- Limit `PKGDEP` generation to a range/tree from `BUILDDEP`, report an error if
  `PKGDEP` is outside of this range (allow disabling in Autobuild3 as a
  workaround/exception).
