+++
title = "Contributor Minutes on 2023-04-08"
description = ""
date = 2023-04-08
[taxonomies]
tags = ["minutes"]
+++

Agenda
======

- [Replace Current Pastebin Site with Microbin](#replace-current-pastebin-site-with-microbin)
- [Omakase Review](#omakase-review)

Replace Current Pastebin Site with Microbin
===========================================

- Potentially problematic: Has no database and uses JSON for storage, may slow
  down over time.
- Missing anti-spamming mechanisms such as attachment size limits.
- Look for alternative implementations.
    - Rustypaste has similar issues.
    - [ascclemens/paste](https://github.com/ascclemens/paste)?

Omakase Review
==============

- `oma rdepends` should list relevant versions of the dependees and reference
  the dependency.
    - "Depended by: tdeio-apt (14.0.13) (apt >= 2:2.2.4)"
- Learn from nala: `oma history` should present changes in a more friendly way
  and allow for redo/undo.
- Crashes when attempting to reinstall a package that is no longer found in any
  repositories.
    - List such packages like `packagekit/[unknown]`.
