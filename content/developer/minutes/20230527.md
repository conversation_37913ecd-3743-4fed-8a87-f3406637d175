+++
title = "Contributor Minutes on 2023-05-27"
description = ""
date = 2023-05-27
[taxonomies]
tags = ["minutes"]
+++

Agenda
======

- [AOSCC 2023 Event Planning](#aoscc-2023-event-planning)
- [OSPP 2023 Mentor Updates](#ospp-2023-mentor-updates)
- [Omakase Review](#omakase-review)
- [LiveKit Review](#livekit-review)
- [KDE Neon Unstable Review: KDE 6 Preview](#kde-neon-unstable-review-kde-6-preview)

AOSCC 2023 Event Planning
=========================

- Drafted a [tentative event page](https://wiki.aosc.io/zh/community/aoscc/2023/).
- Decided that it is acceptable that we pause the live stream during the
  Loongson segment for topical concerns.

OSPP 2023 Mentor Updates
========================

- Lack of experience with DeployKit candidates, will have to cancel project if
  needed.

Omakase Review
==============

- Limit maximal width of the UI (to 150 columns?).
    - Still needs a width limit for `indicatif` elements.
- Omakase segmentation faults with frequent Ctrl-C (fault in signal handler).
- Time to merge into `admin-base`, it’s close enough.
    - Keep `apt` commands around for a good amount of time.
    - Default to `oma` command instructions in documentation.
    - Wait for localization.

LiveKit Review
==============

- Blockers.
    - Some AArch64 devices may require using "text mode" (text console) mode
      for GRUB.
        - No fallback available.
        - Default to console mode on AArch64?
        - Hotkey to switch between gfxmode and console mode (see Ventoy).
    - Switch Pinyin to the first IM candidate.
    - DeployKit does not work: crashes after confirmation.
- Installer enhancements.
    - DeployKit: Select a partition with "acceptable FS" by default (instead
      of selecting the first, which is usually the ESP).
    - DeployKit: Select recommended FS by default, should a whitelisted FS
      (xfs) be the current format.
    - DeployKit: Consider adding a "pretty name" map for locales.
    - DeployKit: Game aborted upon installation completion (god damn it!) and
      the "Installing" window remained behind the "Installation Complete"
      window.
    - DeployKit: Use systemd to reboot the system.

KDE Neon Unstable Review: KDE 6 Preview
=======================================

- *KDE IS A DESKTOP ENVIRONMENT - KDE can bite me (Mingcong Bai).*
- *It's a mess. Nothing to see yet.*
