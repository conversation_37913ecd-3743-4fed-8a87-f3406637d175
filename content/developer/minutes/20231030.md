+++
title = "Contributor Minutes on 2023-10-30"
description = ""
date = 2023-10-30
[taxonomies]
tags = ["minutes"]
+++

Topics
======

Today's meeting focuses on various project planning and updates.

- [Omakase 1.2: Error Output and Debugging](#omakase-1-2-error-output-and-debugging)

Omakase 1.2: Error Output and Debugging
======

- Re-work error output I/O to split "ERROR" and "DUETO" outputs, one line for error description, another line for error code. Mock-up as follows:

```
    ERROR  无法下载 apt_2.6.1_amd64.deb！
    DUETO  下载时遇到网络问题：网络不可达 (os error N)
```

```
    ERROR  无法下载 apt_2.6.1_amd64.deb！
    DUETO  写入 apt_2.6.1_amd64.deb 时发生 I/O 错误：没有这样的文件或目录 (os error 101)
```

- Implement function tracing in key functions, so that Omakase running in `--debug` mode can return the exact functions and code location where the error occurred.
- Investigate error message localisation for both Glibc errno and error codes in `requests`.
    - Do we need everything `requests` returns?
- Reminder for previous to-do items.
    - Missing localisation in `oma history`.
    - Keep backups of configuration files for oma undo (probably in tarballs in set places, marking by package names, versions, checksums, etc.).
    - All `indicatif` strings and elements needs to be marked for justification.
