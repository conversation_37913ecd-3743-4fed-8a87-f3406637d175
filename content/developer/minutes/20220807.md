+++
title = "Contributor Minutes on 2022-08-07"
description = ""
date = 2022-08-07
[taxonomies]
tags = ["minutes"]
+++

Contributor Minutes on 2022-08-07
=================================

Attendees
---------

- <PERSON><PERSON><PERSON> (Canary)
- chez
- <PERSON><PERSON>
- <PERSON><PERSON><PERSON>
- <PERSON><PERSON><PERSON> (Bai)
- RedL0tus (Salted Fish)
- <PERSON><PERSON><PERSON> (Liu)

Agenda
======

- [OSPP Progress Report](#ospp-progress-report)
- [DeployKit GUI RFC](#deploykit-gui-rfc)

OSPP Progress Report
====================

- Bai: Could you please update us on you and your student's OSPP project (AOSC OS Packages re-implementation)?
    - <PERSON>: <PERSON><PERSON><PERSON> is not here, so let's hear from her on another date.
- <PERSON>: My mentee (chez) is here. I feel like that he's already mostly done - the webpage is in good shape and the backend is already working. We have decided to leave out the SQL command line though for the sake of security. <PERSON><PERSON><PERSON> used this command line interface to account for unimplemented features, but since they are now implemented, it's no longer necessary... We still need to work on displaying testing packages (topics) - this functionality was implemented when we were still using the multi-branch model, but it's now broken as we switched to the topic-based model.
    - <PERSON>: Why don't we let your student introduce us to his project?
    - chez: *Loud mic noises*
        - Bai: Just lower your volume, we can hear you.
        - <PERSON>: Introduce us to your work please - I feel like you've done a good job considering your advanced progress.
- chez: I have refactored abbs-meta, which was quite slow in the old backend. I have parallelised tasks where possible - now it should only take 2 - 3 minutes to complete a scan.
    - Liu: It used to take 40 minutes - quite good.
    - chez: I have also introduced some new SQL phrases to optimise size.
        - Liu: The database was used to sync Git-Fossil, which is no longer needed.
        - chez: Yes, and I have now switched to using Rust's libgit2 implementation.
- chez: On the frontend, I have reimplemented the webpage in Rust, all functions are complete and I have addressed some bugs. Also, the upstream version detection now supports the new `SRCS=` syntax. But otherwise, the webpage should function as it used to before (including the JSON/TSV exporting feature).
- Bai: Liu and I have discussed a few possible design changes, he will update you with them later on (more prominent QA link, package details page refinements to make it more readable, and more colour codes for package updates feed).
    - Liu: Yep, we will exchange the design details later.
        - chez: Just make it quick, since my summer break will end very soon (in two weeks).
        - Liu: You will be fine, it's much easier than re-implementing the site.

DeployKit GUI RFC
-----------------

- Bai: Let's demonstrate the new DeployKit GUI mockup to see what you guys think. The UI is almost complete and Liu has already started to implement the IPC backend logics.

(No detailed notes while Liu conducts the demonstration...)

```
Mingcong Bai, [2022/8/7 上午12:53]
[Forwarded from Mingcong Bai]
至于黄色字可能导致注意力问题，我们正在尝试实验按钮激活前不显示黄色

Mingcong Bai, [2022/8/7 上午12:53]
[Forwarded from Mingcong Bai]
就是用户名密码那个页面，比如用户名不合法，按 Tab 或者切换输入框后，显示“用户名包含非法字符”错误的同时，把“用户名”字样也设置成黄色

Mingcong Bai, [2022/8/7 上午12:53]
[Forwarded from Mingcong Bai]
这样方便用户匹配出错的点

Mingcong Bai, [2022/8/7 上午12:53]
[Forwarded from Mingcong Bai]
然后密码匹配错误，改成了“确定密码”那里长度达到原密码一致或超出长度的时候立刻检查并显示不匹配

Mingcong Bai, [2022/8/7 上午12:53]
[Forwarded from Mingcong Bai]
这样就不至于按下一步之后才显示错误

Mingcong Bai, [2022/8/7 上午12:53]
[Forwarded from Mingcong Bai]
然后，用户在输入确认密码（即那一栏有内容的时候）然后再次回到密码输入，就立刻清掉确定密码那一栏，免得浪费用户时间

Mingcong Bai, [2022/8/7 上午12:53]
[Forwarded from Mingcong Bai]
主机名输入框默认生成的随机名称若没有被用户更改，则用户点输入框后，默认全选内容，只要有一个字改了，则无此行为

Mingcong Bai, [2022/8/7 上午12:53]
[Forwarded from Mingcong Bai]
另外这个“时间基准”的说法还得推敲下

Mingcong Bai, [2022/8/7 上午12:53]
[Forwarded from Mag Mell]
时间标准

Mingcong Bai, [2022/8/7 上午12:53]
[Forwarded from Mag Mell]
时间计算标准
```

Actionables
===========

- Liu: Communicate package site design changes with chez.
- Liu: Implement DeployKit UI design changes.
