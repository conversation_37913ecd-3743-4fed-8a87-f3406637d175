+++
title = "Contributor Minutes on 2023-10-15"
description = ""
date = 2023-10-15
[taxonomies]
tags = ["minutes"]
+++

Topics
======

Today's meeting focuses on various project planning and updates.

- [StinkyPaker Implementation](#stinkypaker-implementation)
- [Packages Site: Re-implementation in PostgreSQL](#packages-site-re-implementation-in-postgresql)
- [Omakase 1.1: Go or No-Go](#omakase-1-1-go-or-no-go)

StinkyPaker Implementation
==========================

- GitHub Workflows-based CI implementation for automatic packaging, https://github.com/AOSC-Dev/StinkyPaker.
    - Needs something to parse Markdown (to read and update pull request description) and Bash scripts (to read and update spec files).
    - Markdown should be easier with pattern matching.
    - Implement [APML](https://wiki.aosc.io/developer/automation/apml/) verfication with PyParsing.
        - Update the standards as needed, consider violations unacceptable.
- Two progress indicators in topic pull requests.
    - Replace "in-range" would be necessary.
    - [pycmarkgfm](https://pypi.org/project/pycmarkgfm/) should be able to handle it, by reading out paragraph IDs.
- To enhance robustness, consider exporting that Markdown document as a tree and update by elements.
    - This would also implement a syntax verification in the process.
- Also consider updating the topic pull request templates in the process, as no one seems to be using the "Packages updated to stable" checklist.
- Potential problem - arrays in `defines`.
    - APML does not allow arrays, but `defines` already allows this (for at least `autotools` and `cmake*` templates).
    - But more packages may be using arrays in the future.
    - Consider keyword matching to avoid problematic contents?
        - Since not all options are necessary for scheduling and assembling build environments.
        - Is this possible?
    - Bottom line is, arrays will be used by more packages in the future.
    - May need to consider limiting and setting confines for array usage.
- Also consider setting a specific `IFS=` for the parser (and Autobuild3 for that matter).

Packages Site: Re-implementation in PostgreSQL
==============================================

- Multiple approaches Debian version comparison.
    - PL/SQL operatives in PostgreSQL (`CREATE FUNCTION`) - this may be favourable, but steep learning curve.
    - Server-side PostgreSQL plugin - breaks with each update, could be too much work.
    - Backend logic, outside of the database - easier but unsuitable for large data sets (high RAM costs).
- Syncing with `p-vector`.
    - Import schema matching that of the external database.
    - May need support from liushuyu.
- May take time to train maintainers in SQL programming, but this is nonetheless necessary for acceptable performance.
- Potential performance issue with PL/SQL.
    - Consider setting an acceptable performance baseline, don't worry too much about optimisation for now.

Omakase 1.1: Go or No-Go
========================

- Resolve the following before 1.1 release.
    - `oma list bash@3` and `oma list bash_3` does not report error.
        - Reference https://www.debian.org/doc/debian-policy/ch-controlfields.html#s-f-source for a whitelist.
- Release planning: Save these for after 1.1.
    - Missing localisation in `oma history`.
    - Keep backups of configuration files for oma undo (probably in tarballs in set places, marking by package names, versions, checksums, etc.).
    - All `indicatif` strings and elements needs to be marked for justification.
    - Incorrect line wrapping for long prompts for English interface.
        - Prompt "Please enable debug mode by appending the `--debug` flag to record detailed program output, and submit it along with your bug report at https://github.com/AOSC-Dev/oma" is too long, break up the lines.
        - If too difficult to implement word break in the current prompt implementation, consider breaking up the lines in the translation file.
