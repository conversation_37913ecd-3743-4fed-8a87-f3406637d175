+++
title = "Contributor Minutes on 2022-05-15"
description = ""
date = 2022-05-15
[taxonomies]
tags = ["minutes"]
+++

Contributor Minutes on 2022-05-15
=================================

Attendees
---------
- <PERSON><PERSON> (Saki)
- <PERSON><PERSON><PERSON> (<PERSON>)
- RedL0tus
- <PERSON><PERSON><PERSON> (<PERSON>)
- Icenowy
- CambeR
- cth451
- N<PERSON><PERSON><PERSON>
- <PERSON><PERSON>nda
======

- [Commit Message Conventions](#commit-message-conventions)
- [Packages Violating AOSC OS Packaging Guidelines](#packages-violating-aosc-os-packaging-guidelines)
- [Other Distro Review (4)](#other-distro-review-4)

Commit Message Conventions
===============================
- Bai: Looks like we need to talk about commit message conventions this week. What we need to include in each commit message in abbs repository.
    - CambeR: I haven't made up my mind for this particular topic.
    - Bai: Take a look at these two Google Docs: [RFC document for AOSC development workflow changes](https://docs.google.com/document/d/1PTUldyg88yyuYsewpbtvvTk2MCQ_Toy3cEGmrxeOXpk) and [RFC document for packages violating AOSC OS packaging guidelines](https://docs.google.com/document/d/1TBtWwNYjsXopRo-ZddHTbNtPVJqEjnSXzNfO7Y7ouMw).
    - Saki: I don't think it's a good idea to make commit message review itself to be a separate process. It's not flexible enough.
    - Bai: In other words, to review the commit message as part of the existing review process for pull requests.
    - Bai: However, should we allow someone other than the author to modify the commit message in a pull request?
    - CambeR & Saki: This should be done given the author has permitted that person to make those changes.
    - Cth451: Amending a commit should not be allowed.
    - Saki: Keeping the status-quo might be the safest option.
- Bai: I am actually quite curious if requesting the author to amend their pull request would slow down the process.
    - Icenowy: Should we have a semi-automated script like the `check-patch.pl` in the Linux kernel?
    - Saki: I think you can only expect the author to respond to the request reasonably.
- Bai: How to point out the issues related to the commit message, then?
    - Liu: I think the only window of opportunity you have is the pull request review process itself.
    - Bai: However, we probably don't want to pointing out the commit message issues during or prior to the pull request creation.
- Bai: I also would like to further push the idea that the commit message should be more informative. You can't just write "update" or "change" in the commit message.
    - Saki: However, if the changes are only localized in the `spec` file, I think you probably can't write anything more meaningful than "update" or "change" in the commit message.
    - Bai: That's fair. However, if you changed more than that, you still should write more comprehensive messages.
    - Saki: Though, I think you were doing the right thing regarding to the "last event".
    - Bai: But I am afraid of over-management or over-enforcement of the rules.
    - Icenowy: In which case, I think commit message related issues should not be a blocking issue during the pull request review.
    - Bai: However, if your commit message is really awful, should we still block the pull request?
    - Icenowy: (...*Inaudible*) ... should allow some language related defects in the commit message.
    - Bai: I was not talking about the grammar errors, but the commit message is not sufficiently informative.
    - CambeR: (...*Inaudible*) ... grammer errors should not be a blocking issue.
    - Icenowy: I think we are abusing `abinfo`. Obvious behavior should not be printed out in the script.
    - Bai: Disagree. This could be regarded as a status message so you will know what went wrong if something went south during the build.
    - *Bikeshedding*
    - Icenowy: I think we probably should defer the conversation to later. What commit message should include must be explicit. If not explicitly specified, then it should be allowed.
- Bai: So we should block the pull request according to the importance level. If it is a real serious issue, then we should just block the pull request; otherwise, just make a normal comment.
- Bai: I have another proposal: should all the repositories under AOSC-Dev use pull request mechanism to make changes.
    - Liu: However, many repositories are single-person maintained or owned.
    - Bai: Then, multi-personnel projects should do that?
    - Liu: How to decide how to determine whether a project is a multi-personnel project? If one person only have a few commits, should that project be a multi-personnel project?
    - Bai: Hmm. That's an issue for later.

Packages Violating AOSC OS Packaging Guidelines
===============================================
- Bai: We should talk about how to handle packages that violates AOSC OS packaging guidelines. Specifically, those with telemetry and tracking features, usually proprietary software.
    - Bai: We have already agreed there should be a dialog box informing the user prior to launching the application. Let's talk about what should be on that dialog box.
    - Liu: We can use `zenity` to show the dialog box. Also, the user should be informed that telemetry is potentially not switchable.
    - Bai: Ah, in addition, software with non-removable auto-update features should also be classified to this category. Like, VSCode.
    - *Bikeshedding*
- Bai: Okay, take a look at the Google Docs above, any issues?
    - Saki: I think you should convey the unknown that the telemetry features, although the documentation provided by the vendor stated how to close it, may be still on after the steps.
    - Bai: Hmm. Okay. I will post the draft here and we will collect the comments after the meeting.

Other Distro Review (4)
===============================
- Bai: The distro to review this week was voted to be Kubuntu.
- *Review starts, detailed discussion omitted*

Note: See [the notes here](@/developer/notes/distro-survey-2022.md#kubuntu) for the review summary.


Actionables
===========
TBD
