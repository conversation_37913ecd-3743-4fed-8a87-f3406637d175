+++
title = "Contributor Minutes on 2023-02-18"
description = ""
date = 2023-02-18
[taxonomies]
tags = ["minutes"]
+++

Agenda
======

- [CIP Work Report](#cip-work-report)
- [OSPP 2023 Survey](#ospp-2023-survey)
- [Next Steps for Omakase](#next-steps-for-omakase)

CIP Work Report
===============

- Rust support.
   - Finished reviewing available features for the cross toolchian.
   - Rust won't be available just yet.
   - Rustix contains MIPS R2 assembly, resulting in linker error due to ABI
     incompatibility. Workaround: Use libc (`--cfg=rustix_use_libc`) or
     implement relevant features in R6 assembly.
   - Cross toolchain builds with `--release` but not with `--debug` as LLVM will
     crash (segmentation fault in libLLVM, investigation pending). Most Rust
     tools will build with a cross toolchain.
- AOSC OS for MIPS64 R6.
   - Introduce LLVM 15 and update Linux Kernel to 6.2
   - Rebuild all packages with MSA support (`-mmsa`) and compact branches by
     default (GCC option: `--with-compact-branches=always`). Awaiting MSA
     patches for GCC and Glibc.
       - There are known issues with MSA features in some packages (see our
         [tracking issue](https://github.com/AOSC-CIP-Pilot/aosc-os-abbs/issues/4).
   - `6.0.2-aosc-main` does not seem to boot on Boston, investigation pending.
- Automation. Implementation underway, basic framework complete. Git version
  update checker pending implementation.

OSPP 2023 Survey
================

Completed and submitted the the "开源之夏 2023 社区预调研问卷."

开源之夏 2023 社区预调研问卷

尊敬的社区联系人，

您好！开源之夏 2023 活动即将开始。在此，我们诚挚的邀请您代表社区参与本次问卷调查，反馈您对新一届活动的意见与建议。您的反馈将为活动组委会提供重要参考，帮助我们进一步完善活动流程与规则。感谢您能抽出几分钟时间来参加本次问卷，现在我们就马上开始吧！

1. 请问您的社区是？*

安同开源社区 (AOSC)

2. 请问您对开源之夏 2022 活动的流程与规则是否满意？*

很不满意

3. 您对开源之夏 2023 活动的流程与规则有什么意见和建议？

1. 透明度不足：社区项目配额指标不透明——没有解释配额规则，亦没有文档说明操作规范。 
2. 项目难度规范模糊：如 Google Summer of Code 会对各个难度级别有详细说明（以全职工作工时数为计算方法）——缺乏具体量化规定的情况下，高难度项目亦有可能很快就能完成。 
3. 沟通消极：组委会对社区质询回应不积极，导致许多规则没有充分说明，并导致冲突。 
4. 项目配额：社区之间应该可以转让名额，亦应该有候补机制。
5. 竞争不公平：所谓“社区赞助”实为钱权交易，社区或企业可以用金钱换取更多名额，对无资本运作社区（如本社区）非常不公平，有违中科院作为国家机构政务公正、透明和为人民服务的基本原则。

4. 开源之夏 2023 活动计划升级社区项目上线机制：优化社区赞助，增加社区审核

社区项目上线阶段，组委会根据社区报名情况限定每个社区的支持项目数，但社区可在系统中提交超出组委会支持数量的项目。学生项目申请书审核阶段，在导师审核后，加入社区审核。社区从导师通过的学生申请中，内部讨论决策选出支持数量内的项目，作为组委会支持项目。若希望额外增加项目，则与组委会签署《社区赞助合作协议》，通过赞助形式增加。社区赞助经费全部用于学生结项奖金（含税），赞助经费在该赞助项目成功结项后向组委会支付。

例如：某社区的组委会支持项目数为3个，该社区在项目申请阶段上线8个项目，其中6个项目被学生申请。社区与6位项目导师沟通后，优中选优，决定择取其中 5个项目通过社区审核进入开发阶段，其余项目社区审核不通过。这其中，3个项目为组委会支持项目，额外的2个项目为社区赞助项目。社区与组委会签署《社区 赞助合作协议》。开发结束后，该社区共有4个项目成功结项，其中1个为社区赞助项目，最终社区向组委会支付这1个结项赞助项目学生奖金（含税）金额的赞助 经费。

请问您是否能够理解并接受上述社区审核机制，为什么？*

不接受，原因是：见上一问题答复第五点。

5. 新社区项目申请机制将赋予社区联系人账号更多职责与权限。社区联系人账号可在系统中查看本社区各项目的申请与审核状态；社区审核流程在社区联系人账号中进 行操作，实际审核人员由社区自行决定；为提高活动质量，负责社区审核的人员需了解本社区各项目申请情况，优中选优，择选出社区最值得发展的申请学生。

请问您是否理解并接受上述规则，为什么？*

接受

6. 您想对组委会说的话

作为从首届“开源之夏”开始积极参加活动的社区，我们认为 2022 年组委会的行为严重影响了我社对该活动的能力，并对该活动组织是否能做到公正透明产生了怀疑。中科院作为国家机构，在活动组织和赞助时，通过允许“社区赞助”（实际上说有公司企业等“赞助”项目的操作）规则的存在，有违基本政治原则，值得谴责。望组委会聆听国内社区和志愿者群体的意见，让活动更加公平、公正和透明，并起到“开源之夏”应有的社会服务作用。

Next Steps for Omakase
======================

- `apt-mark`-like subcommands.
- `apt-cache depends/rdepends` equivalents.
- PolicyKit configuration.
- Adapt to Ciel local repositories (add support for "flat" repositories,
  managed by `dpkg-scanpackages`).
- Plugin support (like Ciel and Git).
- Flatpak search and installation support (integration to `oma search`).
    - Make use of FlatHub Beta API or libflatpak.
    - Invoke `flatpak` while installing packages.
    - Consider learning from Bauh.
- Follow-up with the `rust-apt` upstream regarding `fix-broken` behaviors.
- Handle `-dbg` packages via a `install-dbg`-like parameter.
- `oma pick` for picking package versions to install.
- `oma search` interface.
- Review UI strings and prepare for localization.
- `oma provides` may need to invoke `oma refresh`, which needs root permissions.
- `command-not-found` functionalities.
