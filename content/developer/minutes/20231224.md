+++
title = "Contributor Minutes on 2023-12-24"
description = ""
date = 2023-12-24
[taxonomies]
tags = ["minutes"]
+++

Topics
======

Today's meeting focuses on various project planning and updates.

- [KDE 6 体验评测及配置考察](#kde-6-ti-yan-ping-ce-ji-pei-zhi-kao-cha)

KDE 6 体验评测及配置考察
====

需要调整的配置
----

- Plasma 外壳及窗口管理
    - 浮动面板默认关闭
    - 默认关闭桌面概览
    - 默认开启“最小化窗口靠后排列”
    - 默认开启“最小化窗口靠后排列”
    - 关闭所有“窗口管理”特性（性能问题）
- 桌面会话
    - 默认使用 PipeWire
    - 应用 Plasma 外观到 SDDM
- Baloo 文件索引器
    - 仅索引文件名
- KRunner
    - 仅打开如下插件
        - 应用
        - 书签
        - 位置
        - 命令行
        - 常用位置
        - 文件搜索
        - 日期时间
        - 最近文件
        - 活动
        - 电源
        - 窗口
        - 系统设置
        - 终止程序
    - 在“中间”显示并且打开“按任意键开启”
- 默认开启菜单栏
    - Ark
    - Gwenview
    - Konsole
- Gwenview
    - “全屏背景色”设置为黑色
    - 关闭“动画切换效果”
- Dolphin
    - 默认显示侧边栏
    - 默认打开 Tab 切换视图，“显示工具提示”
- 应用菜单 (Kicker)
    - 默认开启“扁平化子菜单到一级”
    - 增加 Alt+F1 快捷键

注意事项及需要报告的问题
----

- SDDM
    - 按回车没有反应？
- 应用程序菜单 (Kicker)
    - 打开二级菜单时，任务栏会显示多余窗口 (`plasmashell (2)`)
    - 菜单徽标大小不正确
- Plasma 外壳
    - 高亮色不会跟着设置修改（而窗口中的高亮色工作正常）
- 系统设置
    - GTK 风格设置默认为空
    - Plymouth 主题没有预览（需要生成截图）
    - 关闭“在通知中显示”应用程序执行进度会导致 Dolphin 彻底不显示进度
    - 待查：“触摸点高亮”是否能在 X11 上使用？
    - “辅助工具”应称为“调试工具”
    - 最大化最小化动画缺失
    - “拼写检查”界面的修改探测有问题（点击一次似乎就会造成设置修改的状态）
    - 待查：“软件更新”何时应用更新（立即或重启时）的设置是否应该禁用？
- Gwenview
    - 待查：“再现意图”是什么设置？
- Dolphin
    - “将压缩文件作为文件夹”选项无法打开
