+++
title = "Contributor Minutes on 2020-05-03"
description = ""
date = 2020-05-04T03:36:33.751Z
[taxonomies]
tags = ["minutes", "dev-automation", "dev-sys"]
+++

## Goals for Spring 2020

Spring 2020 cycle will be a shortened iteration cycle, with a few particular focus:

- Package requests (pakreq, 300+ to be addressed), security updates, and general bugfixes will be set as top priorities (and will be listed in the "Priority Tasks" section).
- Core 7.1 will include more significant updates such as Ncurses 6.2 and GCC 9.3.
- Quality assurance improvements, in accordance to what was listed on the [QA page](https://packages.aosc.io/qa/), in particular with error 431 and 432.
- Styling linting, in accordance to the [AOSC OS Package Styling Manual](@/developer/packaging/package-styling-manual.md).

Apart from these focuses, no other updates will be addressed. Desktop environment and application updates (KDE Applications 20.04) may be addressed but only when time allows.

If completed on time, our iteration will be once again (in forever) aligned with seasons on Earth.

Changes Requested for the Packages Site
---------------------------------------

- Error 431 (library version \[sover\] dependency not met) should be marked red if n > 0.
- Consider only the newest version for most QA reports, ignore older versions.
- Remove opt-*, powerpc, ppc64, riscv64, bsp-*-armel from Packages site, since they are currently suspended or will become Retro ports in the near future.

Changes Requested for the Pakreq Site
---------------------------------------

- Track rejected packages and reasons for rejection.

## Automation

Leo:

- 重新写了 `build.sh`，目前还非常简陋
- ![build.sh](@/img/build_sh.jpg)

Lion:

- Bash 限制了很多可能性
- 需要先做一个工具出来做中间层
- 可以参照之前的 Python 实现，在必要时才使用 Bash 解析
- ab3 如果想重构也可以这样设计
- 先基于 ~~Buildah~~ Podman 做一个离线版本的（本地）构建工具
  - 可以看到效果
- 插件体系
  - 不多，可以再实现
  - 需要考虑将 factory-reset 加回去

Leo:

- 有人看过 buildah 吗
- Systemd core 的问题是 C++ 读不到输出
- 我知道可以用管道魔法，但是觉得很丑
- 现在这么想：
  - 写到一个 Log 文件里

Lion:

- Python 有没有问题？
- 直接调用命令行程序似乎不太妥当
- 这个 Python 包抽象了典型 Linux 命令的调用
  - https://github.com/amoffat/sh

Leo:

- **结论就是我们转用 Python**

Lion:

- 把小小命令行做一下
- Gumblex: 先把解析挪进来？

Yhi:

- 软件工程？
- **会先画一些 UML 图**

Lion:

- 软件工程：需要和各开发者具体讨论并细化

Gumblex:

- https://github.com/AOSC-Dev/aoinb/blob/master/prototype/worktime.py
- （混合整数）线性规划
- 这就是几个方程
- 上面矩阵是解参数的
- 下面方程是解工作分配的
- 没有依赖的问题，依赖在 abbs-dep
- 线性规划解决太慢了

Lion:

- **先研究清楚 buildah**
- **将之前 ciel 的逻辑在 buildah 实现**
- **做一些给打包者小命令行工具，例如给包加版本号**
