+++
title = "Contributor Minutes on 2023-05-21"
description = ""
date = 2023-05-21
[taxonomies]
tags = ["minutes"]
+++

Agenda
======

- [Review Inter-Personal Guidelines Amendment](#review-inter-personal-guidelines-amendment)
- [Deprecate LGTM Label](#deprecate-lgtm-label)
- [OSPP 2023 Mentor Updates](#ospp-2023-mentor-updates)
- [AOSCC 2023 Preliminary Planning](#aoscc-2023-preliminary-planning)
- [Omakase Review](#omakase-review)
- [LiveKit Review](#livekit-review)

Review Inter-Personal Guidelines Amendment
==========================================

- Do not allow discussion of violence or the ambiguous "crime" category in any
  groups.
- Expand immediate-ban offences from discussion of suicide to discussion and
  instigation of physical harm upon any person (self or others).
- Clarify that Tuosai is an offtopic group.
- Clarify that the group administrators reserve all rights to the final
  interpretation of the rules.
- Chinese: clarify 任何地方 => 任何场合.

Deprecate LGTM Label
====================

Deleted LGTM label and removed the CI check for this label, as we found the
LGTM label to be largely redundant with the Approve status. In the future, a CI
can be added to test build scripts (upon successful completion, the CI can set
a "build-passed" label).

OSPP 2023 Mentor Updates
========================

No update to note.

AOSCC 2023 Preliminary Planning
===============================

- The ShanghaiTech University approved the event proposal for AOSCC 2023, to
  take place on July 15 - 16th, 2023
    - University provided a form to register visitors, which will require
      names, ID or passport, phone numbers, addresses, and
      employer/organization information, along with various other personal
      information.
        - Provide Google Forms to handle registration.
        - There is rumor that visitor registration may be loosened, but until
          the rumor is confirmed, we should wait before we publish the Forms.
        - Late registration may be available.
    - Parking will be available on campus.
    - No limit to the number of attendees.
    - Classroom assignment is pending, but most rooms should be available - one
      bigger classroom (lecture hall) and one conference room is available.
- Agenda.
    - Mingcong Bai: Day 1 keynote - year in review.
    - OSPP 2023: Project status and prospectus updates from the mentors or
      students, if the students will attend AOSCC either in person or via video
      conference.
    - Community project updates and demonstrations.
        - Mingcong Bai: Seasonal Survey Topics to help update planning.
        - Mag Mell: Introduction to Omakase.
        - Zixing Liu: Outlook - Infrastructure for Automation (RPC support for
          Ciel, alternative container manager, ...).
        - Mingcong Bai, Icenowy Zheng: New ports - R6, LoongArch, RISC-V.
        - Other ideas to come.
    - Community discussions.
        - Future strategies for package inclusion and maintenance, and
          applications from cross-distribution ecosystems (FlatHub, Snapcraft,
          AppImage, etc.) - moving from binary distribution and towards an
          operating system?
        - Possibility and protocols with writing Chinese language commit
          messages.
        - Desktop experience demo and live review.
        - Free-form discussion on the open source ecosystem.
    - Presentation from industry players (invitations pending).
        - Arch Linux, Deepin, PLCT: Felix Yan.
        - CIP United: YunQiang Su.
        - Codeweavers: Jactry Zeng.
        - Loongson: Dialogue between official and community representatives.
            - Wuhan: Xiang Zhai or Xu.
            - Jiangsu: Huacai Chen.
            - Community: xen0n and Revy.
        - Pine64: TL Lim.
        - PLCT Lab: Wu Wei or other staff.
        - Red Hat: Zamir Sun.
- Demo devices.
    - AOSC OS and AOSC OS/Retro, various architectures.
    - ITAIC 信创 devices.
        - Phytium D2000/8.
        - Loongson 3A5000.
        - Huawei Kunpeng 920s (W510).
        - RISC-V devices.
    - Retro devices.
        - IBM ThinkPad T43.
        - SmartQ Q7.
- Codename vote (Core K).
- Raffle.
- Other planning items.
     - Hotels.
          - Holiday Inn Express near campus.
          - Other locations along the Line 18.
     - Stickers - two pages.
     - GPG Signing Party.

Omakase Review
==============

- Bash "fuzzy match" doesn't seem to work anymore.
    - Apply the same "fuzziness" to oma search and `oma command-not-found`
      (performance concerns).
- See if Clap allows showing all available options (like with `--help`) when
  running subcommand without required parameters (like oma mark).
    - No such function available.
- Limit maximal width of the UI (to 150 columns?).
    - Still needs a width limit for `indicatif` elements.
- Omakase segmentation faults with frequent Ctrl-C (fault in signal handler).
- Time to merge into `admin-base`, it’s close enough.
    - Keep `apt` commands around for a good amount of time.
    - Default to `oma` command instructions in documentation.
    - Wait for localization.

LiveKit Review
==============

- Blockers.
    - DeployKit: Benchmark is still broken - missing benchmark file.
        - New 1MiB test file available at https://repo.aosc.io/.repotest.
    - Input method not available for superuser terminals.
    - Allow booting from the hard disk via GRUB menu (exit 1).
- Installer enhancements.
    - DeployKit: Select a partition with "acceptable FS" by default (instead
      of selecting the first, which is usually the ESP).
    - DeployKit: Select recommended FS by default, should a whitelisted FS
      (xfs) be the current format.
    - DeployKit: Consider adding a "pretty name" map for locales.
    - DeployKit: Game aborted upon installation completion (god damn it!) and
      the "Installing" window remained behind the "Installation Complete"
      window.
    - DeployKit: Use systemd to reboot the system.
