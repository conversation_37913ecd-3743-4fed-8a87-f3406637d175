+++
title = "Contributor Minutes on 2023-05-14"
description = ""
date = 2023-05-14
[taxonomies]
tags = ["minutes"]
+++

Agenda
======

- [Omakase Review](#omakase-review)
- [AOSCC 2023 Venue](#aoscc-2023-venue)
- [OSPP 2023 Update from Mentors](#ospp-2023-update-from-mentors)

Omakase Review
==============

- Bash "fuzzy match" doesn't seem to work anymore.
    - Apply the same "fuzziness" to `oma search` and `oma command-not-found`
      (performance concerns).
- Mark full matches in `oma search` with specific tags like "(full match)" or
  list results separately with subtitles.
- `oma list` shows all candidate versions as installed.
- Show actual number of packages downloaded with `oma download`.
- See if Clap allows showing all available options (like with `--help`) when
  running subcommand without required parameters (like `oma mark`).
- Globbing does not work with `oma list`.
- Limit maximal width of the UI (to 150 columns?).
- Omakase segmentation faults with frequent Ctrl-C (fault in signal handler).
- Time to merge into `admin-base`, it’s close enough.
    - Keep `apt` commands around for a good amount of time.
    - Default to `oma` command instructions in documentation.

AOSCC 2023 Venue
================

- Submitted event proposal to teacher who is in charge of club activities.
    - AOSCC is reported as a collaborative club activity.
- Date set tentatively to July 15 – 16th.
- Awaiting review from teacher, should have news next Monday.
- Contingency plans: CIP, TUNA (who will host Debian Release Party on June
  10th), Shanghai LUG, CNRV/PLCT, Loongson (Wuhan), UESTC.

OSPP 2023 Update from Mentors
=============================

- Zixing Liu: DeployKit.
    - Two candidates - one with Rust background and another with Java/Vue.js
      background - not ideal, an examination will be required.
    - Consider requesting students to prepare for basic knowledge on Rust and
      other technical prerequisites.
- Camber Huang: Autobuild3 testing framework.
    - Three candidates - two known peers and one via email, referred one
      student with Rust background to Zixing’s project, but that student
      indicated concerns about lack of experience.
    - One student (A) indicated experience with Debian packaging but appears
      less competitive in other aspects. One student (B) packages for Arch Linux
      RISC-V at the PLCT Lab and the other (C) works on openEuler and AUR.
    - Asked students to familiarize themselves with Autobuild3 and AOSC OS packaging.
    - Examination required.
- Mingcong Bai: Localization.
    - Three candidates, one obviously a lot more proactive, another student
      submitted an application but does not indicate any experience in
      localization work, the third did not submit any materials.
    -  Will examine all candidates for fairness.
- Timeline.
    - Community gets June 5 - 11th to review applications.
    - June 4th meeting to exchange examination materials.
    - June 10th deadline for examination submission (move meeting that week to Saturday).
- Regulations.
    - In principle, allow usage of generative AI platforms (should quality
      suffice), but require disclosure of usage.
    - Requirements for code comments and documentation.
