+++
title = "Contributor Minutes on 2023-02-12"
description = ""
date = 2023-02-12
[taxonomies]
tags = ["minutes"]
+++

Attendees
---------

- <PERSON>
- chenx97
- <PERSON><PERSON>
- <PERSON><PERSON><PERSON> (<PERSON>)
- <PERSON><PERSON><PERSON>
- <PERSON><PERSON> (<PERSON>)
- <PERSON><PERSON> (<PERSON><PERSON>)
- <PERSON>
- TheSaltedFish (Sus)
- <PERSON><PERSON><PERSON> (<PERSON>)

Agenda
======

- [CIP Work Report](#cip-work-report)
- [Package Retirement](#package-retirement)
- [AOSC OS Desktop Review](#aosc-os-desktop-review)
- [Omakase Review](#omakase-review)

CIP Work Report
===============

- Mainline distributions now available as normal tarballs and EvaluationKit (a
  ready-to-use Qemu distribution complete with a disk image and VM start-up
  scripts).
- [Initial design](https://drive.google.com/file/d/1P0EYij_XdEG9kkm15Cao_1L9uTcCeml-/view?usp=sharing)
  done for GitHub Workflows-based update checking, building, and uploading
  mechanism. This change will be adopted for the AOSC upstream for use with
  frequently updated packages (such as Vim and `youtube-dl`).

Package Retirement
==================

- Compiled and published a [news post](https://aosc.io/news/posts/2023-02-12-package-retirement-20230211/)
  regarding this matter.
- Dropped packages and introduced the [Hunter](https://github.com/AOSC-Dev/hunter)
  tool via the package-retirement-survey-20230212` topic.
- Packages site needs addressing by original maintainer (who would be away
  until the end of February) to reflect the changes.

AOSC OS Desktop Review
======================

Verified fixes for the following items:

- Application: Replace Skanpage (skanpage) with the more usable Simple Scan
  (simple-scan).
- Firefox: Text files are opened by LibreOffice by default, should use KWrite
  instead.

Omakase Review
--------------

- Download size and storage usage change statistics should be displayed before
  the list of changes (table), but this will require a re-implementation of
  the interface logic.
- Instead of the actual .deb file name, display package name + version +
  architecture.
- Not enough contrast between total and individual progress indicators.
- No output printed when dpkg configuration fails and retries.
