+++
title = "Contributor Minutes on 2021-11-21"
description = ""
date = 2021-11-21
[taxonomies]
tags = ["minutes"]
+++

Contributor Minutes on 2021-11-21
=================================

Attendees
---------

- cth451
- <PERSON><PERSON><PERSON>
- <PERSON> (Bai)
- <PERSON><PERSON> (OriginCode)
- Kexy Biscuit (Kexy)
- RedL0tus
- <PERSON><PERSON> (Liu)

Agenda
======

- [Twitter and Other Social Media Accounts](#)

Twitter and Other Social Media Accounts
=======================================

- Bai: How should we proceed to restart our Twitter? Kexy mentioned to keep original posts but to remove inappropriate content.
    - OriginCode: I think it would be fine.
        - <PERSON>: But what if we plan to post in English on Twitter?
    - OriginCode, Kexy: Yes, English on Twitter for now, and Chinese for Weibo.
- Kexy: Twitter still has 140 character limitation.
    - OriginCode: So we should start writing abstracts.
    - <PERSON>: We can use threads to workaround this issue.
    - OriginCode: Also depends on in what format we send tweets.
        - <PERSON>: I think this will be fine.
    - Liu: Different submitter will leave different signatures.
        - OriginCode: We've been doing this for our news posts already.
            - <PERSON>: We should use abbreviated forms to save on characters.
            - Kexy: Ditto (^KB).
- Bai: What if we rename the current account to @aosc_dev_cn, and create a new @aosc_dev for English contents.
    - Kexy: I think that'd work.
- OriginCode, Bai: So who would be interested in managing our Weibo?
    - OriginCode: I personally don't like using it.
    - Bai: I don't mind doing it - I managed AOSC's account before, but I'm not sure if my U.S. phone number would work.
        - Liu: We'd need to find that old account again.
            - Bai: Sure, I'll take a look.
            - Kexy: I can do it.
- Liu: What should we post on Twitter/Weibo?
    - Bai: Every news post.
        - Liu: We can use IFTTT in that case.
            - Bai: It doesn't do the job well enough, we should take care of abstracts.
            - Kexy: It doesn't have that kind of humanness, bad for interaction.
    - Bai: Spontaneous development updates, Retro device booting AOSC OS/Retro - for publicity.
        - Kexy: Twitter is mostly for interaction, this would do.
        - OriginCode: I agree.
- Bai: Okay, so whose e-mail should go under each account?
    - Liu: Open a dedicated mailbox - <EMAIL>?
    - Bai: Does Twitter require an e-mail per account?
        - JN: Of course.
            - Bai: <EMAIL>, <EMAIL>?
            - JN: "CN" would likely cause political issue?
                - Bai: Let's not even worry about it.
- Bai: Just use community logo for avatar?
    - Liu: And April Fools avatar.
    - Bai: Language-specific markings?
        - Kexy: Let's not.
    - Bai: Let's leave Bio for later? We don't have to decide right now.
- Bai: Just some remark on rules - let's prohibit reposts unless it's from a community contributor, and about AOSC projects?
    - Liu, OriginCode: This is important.
    - Bai: Because Artoria2e5, who managed the account, reposted political commentaries.
    - Kexy: Let's require "quoted retweet," we should at least comment on what we retweeted.
        - OriginCode: Or only allow commenting?
            - Kexy: That'd be another issue.
            - JN: This could change on the mobile end, I'd say we stick to retweets.
                - OriginCode: All right.
- Bai: What's for the first tweet? Hello world, or start with CodeWeavers?
    - OriginCode: Either way works.
    - Liu: I don't think this matters too much.
    - Kexy: 👀 will do.
    - OriginCode: Or start with An-An?
- Bai: Holiday celebration, but only for internationally (and localised) holidays.
    - OriginCode: Sure, a good way to start interactions.
    - Kexy: Yeah, that'd work, but don't even attempt to prevent political arguments.
- Bai: Mascot representation?
    - Liu, OriginCode: Let's leave that for April Fools.

Actionables
===========

- OverFullHbox: Create e-mail accounts for Twitter.
- Bai, Kexy, OriginCode: Start working on account creation, bio, etc.
