+++
title = "Contributor Minutes on 2022-11-27"
description = ""
date = 2022-11-27
[taxonomies]
tags = ["minutes"]
+++

Contributor Minutes on 2022-11-27
=================================

Attendees
---------

- Camber
- cth415
- <PERSON><PERSON>
<PERSON><PERSON><PERSON> (Icenowy)
- <PERSON><PERSON> (Saki)
- <PERSON><PERSON><PERSON> (Bai)
- <PERSON><PERSON><PERSON> (Liu)

Agenda
======

- [BuildKit: PEP517 Discussion](#buildkit-pep517-discussion)
- [Autobuild ABTEST Discussion](#autobuild-abtest-discussion)
- [CIP/mipsr6el Transparency Report](#cip-mipsr6el-transparency-report)
- [Package Drop Discussion](#package-drop-discussion)

CIP/mipsr6el Transparency Report
===================

- Bai: <PERSON><PERSON> can you give us an overview of the updates from the CIP-related work?
  - Cyan: Sure. Bai and I worked on the documentation after the base system was built recently.
  - Cyan: This documentation was more of a textbook-like material for beginners/interns who just graduated from the university. We will introduce AOSC OS-specific tools and community information in the documentation as well. Basically any details we could think of will be included.
  - Bai: Also the workflow details. How should CIP-related work be included in the AOSC upstream projects.
  - Bai: An issue currently we are experiencing is the lack of information from CIP and related parties.
  - Bai: How should the documentation and training materials organised and what to include depend heavily on the target audience. However we have no idea how skilled the new-hires are.
  - Bai: On subject of software, we do have two different system images, one with MSA enabled and one without. These are not intended for the end users, they are more for demostration and benchmarking. In lieu of this, we only needs to make sure it boots -- whether it has a good UX is irrelevant to us at the moment.
- CambeR: Can CIP provide us an access to their mips64r6 hardware? Or does that even exist?
  - Bai: That's why we are still using emulators. Their Internet connectivity is atrocious, but they do have a prototype device based on a FPGA board.
- Bai: Liu probably already knew this, but we now have something called `EvaluationKit`: a system image contains a collection of various scripts for evaluation purposes.
  - Bai: But we are still unsure about the technical details. We will figure it out in the near future.

BuildKit: PEP517 Discussion
===================

- CambeR: I found some issues regarding PEP 517 packaging. BuildKit does not yet include appropriate dependencies.
  - CambeR: Some people including me voiced against including those dependencies by default in the BuildKit. For instance, Rust or Go compilers are not included in BuildKit.
  - CambeR: And I suggested to handle this per-package-basis. But Icenowy said this is too complicated.
  - Bai: I think we should just bundle Rust and Go into the BuildKit. What do you think?
  - Liu: However then you will introduce LLVM and Clang into the BuildKit. It would be too large, and some architectures may not support it.
  - Bai: That's not an issue. Besides, all our currently supported architectures have LLVM support.
  - Liu: Some packages may silently prefer Clang over GCC. This change may also change their behavior.
  - Bai: That's better isn't it? We can now see which package have issues with configurations.
  - *A lot of bikeshedding later*
  - Liu: Fair enough.
  - Bai: Though we need to put PEP 517 at a lower priority than the regular Python `ABTYPE`. The reason being some of the packages may not build with PEP 517 at the moment.

Autobuild ABTEST Discussion
===================

- CambeR: About the Autobuild ABTEST framework, I am thinking about resuming the development in the upcoming weeks.
  - CambeR: Before that, I have some thoughts to share. I am not sure what should be done with the framework prior to the general availability.
  - Bai: Do we even have a phase like that in our community? Just throw it in, otherwise nobody would notice.
  - CambeR: Okay, let me strike that out from my notes. Hm. Although will the current design of specification files too confusing?
  - Bai: I need a recap with the design details, it was quite a while ago since we last heard about this thing.
  - cth451: What's the goal here? Test the package being built? Also, will it be tied to the `ABTYPE` of the package?
  - CambeR: Yes, but it should not be tied to the `ABTYPE` of the package. But we could still use templates.
  - cth451: Speaking of the templates, the testing routine might be very complicated.
  - Liu: We are only going to run simple integration tests instead of the unit-tests shipped with the upstream project.
  - cth451: Personally, I believe most of the times, we only need to verify whether the compiled binary could be executed.
  - Liu: Yeah. Most of the unit-tests may fail on some of the lesser-known architectures.
  - CambeR: Our current design was to put the test scripts written by the package maintainers in the `test` folder inside the `autobuild` directory.
  - Bai: I think it's still too early to discuss the technical details. Without a prototype, it's very difficult to raise any questions or concerns.
  - Bai: Though I would like to know what kind of other information you want to know about this framework.
  - CambeR: The integration with the QA framework. If no test is detected, a warning should be generated.
  - CambeR: Also the test should be run with the stripped binary.
  - Bai: It's not the unit-test, so of course it will run with the final binary.
  - CambeR: About the type of the tests that need to be run... Even if unit-tests should be run, it should be package maintainer's responsibilities.
  - Bai: Sounds good.
  - CambeR: That ends my questions.

Package Drop Discussion
=======================

- Bai: This is going to be a hugely controversal topic, so we are only going to have an initial discussion today.
  - Bai: We won't be coming to any conclusions today, but we should still start to prepare for this action. We should start drafting a RFC out of this today.
  - Bai: A bit of the background, we have a lot of packages that are not well maintained and they are non-functional, like RStudio.
  - Bai: We should just drop those packages and re-direct the users to use cross-platform solutions like Flatpak.
  - Bai: We may need to get an initial list before next year. An interesting thing is that should we drop any of the desktop environments.
  - Saki: I can make a tool to get the list of removed packages after the package drop using the Dpkg status information.
  - *Some more bikeshedding around how to drop the packages*

Actionables
===========

TBD
