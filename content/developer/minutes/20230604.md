+++
title = "Contributor Minutes on 2023-06-04"
description = ""
date = 2023-06-04
[taxonomies]
tags = ["minutes"]
+++

Agenda
======

- [Review OSPP 2023 Candidates](#review-ospp-2023-candidates)
- [Changes to the devel-base-metapackage](#changes-to-the-devel-base-metapackage)
- ["Roll-Up" Type Topics](#roll-up-type-topics)
- [AOSCC 2023 Planning](#aoscc-2023-planning)
- [Omakase Review](#omakase-review)

Review OSPP 2023 Candidates
===========================

Reviewed all applicant materials and designed examination materials, available [here](https://repo.aosc.io/aosc-documentation/ospp-2023/).

Changes to the devel-base Metapackage
=====================================

- Remove or neutralize `hook-uname`.
- Add `git`.
- Make a `packaging-base` with `autobuild3`, `acbs`, and `ciel`.
- Update BuildKit recipe.

"Roll-Up" Type Topics
=====================

- Problematic: Previously, larger topics (like LLVM updates) are difficult to review and test, and usually ended up under-tested.
- A new topic type for larger, survey-type topics arranged by set schedules: by monthly or seasonal "blocks" (3, 6, 9, 12 months), distinguished by labels.
    - Assigning a person to each topic who organizes (topic organizers) review, rebase, and testing activities at weekly meetings.
    - Topic organizers may propose a tree-wide freeze for specific packages handled in "roll-up" topics.
    - Urgent updates may be removed from these topics for more timely updates (such as Firefox and Thunderbird).
- Pragmatically handle version freezes for "roll-up" type topics (LLVM may benefit from version updates on the way, others may not, such as Web application updates).
- Integration with AppStream XML-type metadata to denote roll-up updates in Omakase.
- Potential issue: repeated rebase, builds, and testing may drag topic iterations even longer.
- Start procedual trial at the next meeting.

AOSCC 2023 Planning
===================

- Loongson Dialogue: Live stream during designated time period, but continue conversation after the period with no live stream for off-the-record segment.
- Internet connection.
    - Eduroam will be available.
    - 20Mbps uplink required, but 100Mbps will be available.
- Open a Bilibili account for the community.
- Address change: 上海市浦东新区华夏中路393号 信息学院 1C-101.
- Canary to correspond with nearby hotels for potential discount (report 30 potential rooms at two persons each, between July 14th and 17th).
    - 全季酒店上海张江店.
    - 和颐至尚酒店上海张江金科路店.
    - 假日智选酒店上海张江店.
- Prepare a poster for WeChat/Web promotion.
- Remove Felix Yan's talk as he would like to abstain from a program for this year.
    - Move lightning shows up.
- To confirm: CIP United, CodeWeavers, GeekPie, PINE64, PLCT Lab, and SHLUG outreach programs.

Omakase Review
==============

*No changes.*

- Uneven progress bar lengths when elipses are used.
- Limit maximal width of the UI (to 150 columns?).
    - Still needs a width limit for indicatif elements.
- Omakase segmentation faults with frequent Ctrl-C (fault in signal handler).
- Time to merge into admin-base, it's close enough.
    - Keep apt commands around for a good amount of time.
    - Default to oma command instructions in documentation.
    - Wait for localization.
