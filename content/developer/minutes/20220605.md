+++
title = "Contributor Minutes on 2022-06-05"
description = ""
date = 2022-06-05
[taxonomies]
tags = ["minutes"]
+++

Contributor Minutes on 2022-06-05
=================================

Attendees
---------
- <PERSON><PERSON> (Saki)
- RedL0tus
- <PERSON><PERSON><PERSON> (<PERSON>)
- Icenowy
- CambeR
- <PERSON> (<PERSON>)

Agenda
======

- [Distribution Adjustments for ARM and RISC-V Deivces](#distribution-adjustments-for-arm-and-risc-v-deivces)
- [Other Distro Review (6)](#other-distro-review-6)

Distribution Adjustments for ARM and RISC-V Deivces
===================================================

- Icenowy: First the [Google Docs: RFC draft](https://docs.google.com/document/d/1jbRz3ZYgKZokHC9jpHDfYccTnLDt1rw3xXMS-HEGMbU).
    - <PERSON>: So you mean we need to switch to a technology called EBBR to boot ARM or RISC-V devices?
    - Icenowy: Yes. However, this would create certain difficulties for the users to install the operating system.
    - Liu: Is that because they need to write the EBBR firmware to their device prior to the installation?
    - Icenowy: Well, the issue here is that we probably need to vendor the device-specific EBBR firmware.
    - Liu: According to the documentation you provided, the EBBR firmware needs to provide certain bootloader services. Does that mean EBBR firmware contains a U-Boot?
    - Icenowy: Correct. U-Boot can certainly provide these bootloader services.
    - Liu: It also said a compatible UEFI implementation must be present.
    - Icenowy: "Compatible". Yes, U-Boot could provide a somewhat usable EFI implementation.
- Liu: Next question, according to your RFC, you wrote "Requires mature mainline". How do we interpret this statement?
    - Icenowy: This means that we need to pin the DT (device tree) to a specific revision.
    - Liu: Pin to a specific version? Why is that?
    - Icenowy: Device tree is then bound to a specific device, and we include the DT blob inside the firmware.
    - Liu: Does that mean when the device tree file inside the upstream kernel tree changes, we need to update the device tree file in the firmware?
    - Icenowy: The upstream kernel tree guarantees that all the device tree changes are backward-compatible. However, there may be missing SoC features.
    - Liu: So, do we need to do anything when the upstream kernel updates?
    - Icenowy: Nothing. But you see EBBR requires a relatively stable -- or in another word -- mature device tree file present in the mainline kernel.
    - Liu: Is there other considerations regarding this device tree topic?
    - Icenowy: No. In case it's still not clear, EBBR requires a device tree that strictly follows mainline kernel specifications, including DT bindings.
- Liu: Okay. Are we going to all-in the EBBR migration?
    - Icenowy: No. Because we can only migrate a subset of devices to EBBR.
    - Liu: For newer devices ...?
    - Icenowy: Well no. We migrate older devices that have better mainline kernel support.
    - Liu: According to the ARM EBBR specification, they said "EBBR compliant platforms should use dedicated storage for boot firmware images and data, independent of the storage used for OS partitions and the EFI System Partition" (Chapter 4). How do we interpret this statement?
    - Icenowy: We don't. In our case, we leave the user to choose where they want to put the EBBR firmware image.
    - Liu: Should LiveKit support writing EBBR firmware images?
    - Icenowy: No. Because this would become a chicken-egg problem, as you need EBBR-capable environment to boot up LiveKit in the first place.
    - Liu: So the user has to figure out the EBBR firmware writing process themselves.
- Icenowy: Another topic I would like to talk about is to reduce the number of raw image distributions.
    - Liu: For non-EBBR devices?
    - Icenowy: Yes. Non-EBBR and non-EFI-compliant devices.
    - CambeR: We can make an image builder intended for the users to assemble the image themselves.
    - Icenowy: Like the one used in OpenWRT?
    - Liu: That will be rather difficult to do. Consider all the different board types and different environments. It's going to be a huge undertaking.
    - Liu: So the normal LiveKit ISO will now be used by SBSA or whatever it's called and those newly supported EBBR platforms?
    - Icenowy: Correct. Although you would also need a way to interact with the LiveKit like a keyboard interface or UART.
    - Liu: So the goal is to reduce the workload of generating all those raw image files.
    - Icenowy: I also want to make "not-so-raw" raw images with some assembly required.
    - Liu: Which means the user needs to assemble the final image themselves?
    - Icenowy: Yes.
    - Liu: Well, then we still need a tool to assemble the final image from the prebuilt components.
    - Icenowy: Yes. But we can leave the details of this tool to another day.
- Liu: In conclusion, we agreed that for EBBR-capable devices, their installation will be merged into existing SBSA-capable devices. While other devices will have prebuilt components and we will provide a tool to assemble the final image for the user.
    - Icenowy: Although, it would not make sense to use EBBR to load LiveKit if there is no way to interact with the LiveKit, or more specifically, DeployKit installer.
    - Saki: I can add an automatic installation mode to DeployKit.
    - Liu: However, like we previously discussed, you wouldn't know where did the user put the EBBR firmware image. According to the EBBR specification, it's possible to put EBBR firmware image inside the root partition, although not recommended.
    - Saki: That's a quite problematic issue.
    - Liu: I think let's just limit the "EBBR-capable devices" to any device that has a way to interact with externally, including keyboard or UART communication. And of course, they have to support EBBR itself in the first place.
    - Icenowy: Sounds good to me.
    - Liu: I think we need to document this later.
- Liu: Next question, you referenced the Armbian's U-Boot boot script here. What do you mean here?
    - Icenowy: I think our current boot script is a bit too inflexible. User needs to have the ability to configure more stuff.
    - Liu: I bet Bai certainly would not approve this. He would probably argue that we need to have a sensible default.
    - Icenowy: Some SoCs have non-detectable hardware features that can only be enabled or disabled through U-Boot bootloader scripts.
    - Liu: Fair enough. So more configurations in the bootloader scripts.
    - Icenowy: For more advanced usage, users can modify the boot script themselves if they so desired.


Other Distro Review (6)
=======================

- Liu: Bai is not present today, but the distros to review this week were voted to be UOS and MX Linux.
- *Review starts, detailed discussion omitted*

Note: See [the notes here](@/developer/notes/distro-survey-2022.md#mx-linux) and [here](@/developer/notes/distro-survey-2022.md#uos) for the review summary.

Actionables
===========

Icenowy: Continue revising RFC for ARM/RISC-V image distribution changes.
