+++
title = "Contributor Minutes on 2023-12-03"
description = ""
date = 2023-12-03
[taxonomies]
tags = ["minutes"]
+++

Topics
======

Today's meeting focuses on various project planning and updates.

- [Omakase: Rename to oma](#omakase-rename-to-oma)
- [Review: oma 1.2 Release Readiness](#review-oma-1-2-release-readiness)

Omakase: Rename to oma
======================

As Omakase really doesn't capture the spirit of the project any more (as the original Omakase implementation rebuilds the system based on a central manifest "recipe," like <PERSON> and that particular way of eating sushi that some seemed to like), we have decided to just rename "Omakase" to "oma."

Review: oma 1.2 Release Readiness
=====================================

- When setting an invalid hostname in sources.list and attempting to run `oma refresh` on AT&T's network, the command floods the terminal with a large amount of empty lines.
    - Adding the `--debug` switch reveals that AT&T returned an HTML in place of the hostname.
    - This is likely an ISP-specific issue.

    ```
    2023-12-03T06:15:31.116735Z ERROR oma: 位于 ⁨<html><head><meta http-equiv="refresh" content="0;url=http://dnserrorassist.att.net/search/?q=http://repo.o%2Fdebs%2Fdists%2Ffrontier%2FInRelease%26srchgdeCid%3DHTqaaaaa%26t%3D0%26bc%3D"/></head><body><script type="text/javascript">window.location="http://dnserrorassist.att.net/search/?q="+escape(window.location)+"&r="+escape(document.referrer)+"&t=0&srchgdeCid=HTqaaaaa&bc=";</script></body></html>⁩ 的 InRelease 文件解析失败。   DUE TO 0: at line 1:
    <html><head><meta http-equiv="refresh" content="0;url=http://d
              nserrorassist.att.net/search/?q=http://
    
    ... (infinite amount of empty lines)
    ```

    - Fetching http://repo.o via cURL with an "unknown" user-agent string results in the following.

    ```
    curl -v -A omakase/1.0 http://repo.o
    *   Trying ***************:80...
    * Connected to repo.o (***************) port 80
    > GET / HTTP/1.1
    > Host: repo.o
    > User-Agent: omakase/1.0
    > Accept: */*
    >
    < HTTP/1.1 200 OK
    < x-correlation-id: LYztkRPw
    < date: Sun, 03 Dec 2023 06:28:02 GMT
    < content-length: 366
    < content-type: text/html; charset=utf-8
    <
    * Connection #0 to host repo.o left intact
    <html><head><meta http-equiv="refresh" content="0;url=http://dnserrorassist.att.net/search/?q=http://repo.o%2F%26srchgdeCid%3DLYztkRPw%26t%3D0%26bc%3D"/></head><body><script type="text/javascript">window.location="http://dnserrorassist.att.net/search/?q="+escape(window.location)+"&r="+escape(document.referrer)+"&t=0&srchgdeCid=LYztkRPw&bc=";</script></body></html>
    ```

    - It seems that `oma` did not handle this case correctly - `InRelease` parser had failed but it nonetheless proceeded to parse the file.
        - Should also check Content-Type header and match known type for `InRelease` files.
        - The error message also erroneously showed the HTML body (`<html><head><meta http-equiv="refresh" content="0;url=http://dnserrorassist.att.net/search/?q=http://repo.o%2Fdebs%2Fdists%2Ffrontier%2FInRelease%26srchgdeCid%3DHTqaaaaa%26t%3D0%26bc%3D"/></head><body><script type="text/javascript">window.location="http://dnserrorassist.att.net/search/?q="+escape(window.location)+"&r="+escape(document.referrer)+"&t=0&srchgdeCid=HTqaaaaa&bc=";</script></body></html>⁩`) instead of the URL (`http://repo.o/debs/dists/frontier`).
- `oma history` did not handle parse errors correctly when running as a regular user.

    ```
    ./target/release/oma history
        ERROR Unable to parse your locale: ParserError(InvalidLanguage)
        ERROR Failed to perform file operations in ⁨/var/log/oma/history.db⁩.
       DUE TO Permission denied (os error 13)
    ```

    - When the same command is run with `sudo`.

    ```
    sudo ./target/release/oma history
        ERROR Unable to parse your locale: ParserError(InvalidLanguage)
        ERROR Failed to parse an object in the history database.
       DUE TO missing field `is_success` at line 1 column 5267
    ```
