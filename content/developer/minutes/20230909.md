+++
title = "Contributor Minutes on 2023-09-09"
description = ""
date = 2023-09-09
[taxonomies]
tags = ["minutes"]
+++

Topics
======

Today's meeting focuses on Ciel, ACBS, and other CI progression. We also discussed Omakase 1.1's changes and reviewed their effectiveness.

- [Ciel Changes](#ciel-changes)
- [ACBS Changes](#acbs-changes)
- [Transferring CI Implementation](#transferring-ci-implementation)
- [Omakase 1.1 Review](#omakase-1-1-review)

Ciel Changes
============

- Original idea - an /etc global override for container settings?
    - Setting a default "local repository" or "source" to pull tarballs and packages from.
    - So that people wouldn't have to ask around for local repository settings.
- Extending the original idea to a template, `ciel new --from-template` to allow for automation; otherwise, ask the user whether to use detected templates.
    - Use the current `.ciel/data/config.toml`.
    - Consider supplying a sample.
- Merged [#19](https://github.com/AOSC-Dev/ciel-rs/pull/19): "feat: add squashfs extract system support."
    - Need to add `squashfs-tools` dep.
- Regarding [#12](https://github.com/AOSC-Dev/ciel-rs/pull/12): "cli: operation delall to remove all instances."
    - It covers up a design flaw (only one parameter could be supplied).
    - Implementing this as a workaround is not favourable compared to fixing the root cause.

ACBS Changes
============

- Regarding [#21](https://github.com/AOSC-Dev/acbs/pull/21): "fetch: try use gix to fetch package src."
    - Upstream released a partial fix, suppressing the error output when fetching and initialising submodules, but Gix still could not handle submodules.
    - One option is to implement a fallback to handle submodules.
- Regarding [#20](https://github.com/AOSC-Dev/acbs/pull/20): "pm: adapt oma package manager."
    - Need to add `--no-progress` to the default option and a check for whether this option exists.
    - This is a 1.1 feature, wait for Omakase 1.1.
    - Or set a dependency requirement/check for Omakase 1.1.

Transferring CI Implementation
==============================

- CIP United decided that it wants a CI coverage using AOSC OS as an environment, but *not* to build a distribution with CI.
    - They will use Jenkins instead.
    - The current implementation will be moved to AOSC-Dev.

Omakase 1.1 Review
==================

- Typo in `oma rdepends` warning about "unexpected\* pattern: ..."
- Record timestamps instead of date-and-time strings in operation history database, in order to prevent confusion or inconsistency as the UI drawing behaviours change.
- Drop purge-as-default and differentiate `remove` and `purge`.
- Keep backups of configuration files for `oma undo`.
    - Where should we keep them?
- Use past tense for all `history`/`undo` entries.
