+++
title = "Contributor Minutes on 2020-05-24"
description = ""
date = 2020-05-24T03:57:40.753Z
[taxonomies]
tags = ["minutes"]
+++

AOSC Moe-ification
------------------

Originally as designed by <PERSON><PERSON> ...

> 对「安安」的人设： 
>  
> 1. 黑色的头发，但不整齐，会稍微有凌乱。 
> 2. 黑色的眼睛。 
> 3. 不喜欢整理房间，房间乱糟糟。 
> 4. 三围参考 Flip Flappers 的パピカ，也许是个好主意。 
> 5. 对于 AOSC 那四个颜色怎么用，颜色要改成那种鲜明和淡一点的。回家电脑我试试调调色（我感觉没什么好办法）

- Liu: Artist (<PERSON>) is a character designer, so we should loosen our requirements.
    - <PERSON><PERSON>: Understood.
    - Bai: How should we proceed?
- Liu: Let's focus on characteristics and history.
- Bai: "Flip Flappers 的パピカ"?
- <PERSON>ki: Make her "dirty."
    - Bai: In what sense? What does this "dirtiness" relate to our community? Let's get rid of the fourth point, since that's really a job for <PERSON>.
- Bai: Humanising your experience at the community? Multilinguist, Poor, Friendly (but introvert).
    - Sa<PERSON>: That sounds like enough, and plus our basic guidelines (hair and eye colours).
    - <PERSON>: Ask designer to take a look at our logos?
- <PERSON>: Background story? Retro?
    - <PERSON><PERSON>: Let's just go with what we have - we can redesign her story around our history.
    - <PERSON>: He should be able to find it on our website.
- Lion: Multiple characters?
    - Liu: That might be too much.
- <PERSON>: Race or species?
    - <PERSON>: Let him design and we will go from there.
    
Autobuild3
----------

- Bai: Recent commits does not change Autobuild3's default behaviours, but enables LTO for non-Retro architectures.
    - `AB{2,3}{,SHORT}VER` helps with using Python versions in scripts, eliminating the use of hard coded versions.
- Lion: Do we have a linter for Autobuild3 syntax yet? [(Broken link)Packaging Metadata Syntax](/dev/sys/packaging-metadata-syntax).
    - Gumblex: We have `bashvar.py` in [abbs-meta](https://github.com/AOSC-Dev/abbs-meta/).
        - Lion: I will implement this later today.
    
AOINB
-----

- Bai: What's going on with the project (now that it hasn't been touched on for over a week?
    - Gumblex, Lion: Busy.
- Bai: What is the next step?
    - Gumblex: Quality assurance and automated testing.
    - Bai: Will implement QA in Autobuild3 before the next weekend.
- Leo: We need to start on a "dummy scheduler" that helps with invoking `builder` from the command line (on a local machine). However, I have no idea how to implement dependency resolver, and we need to decide on a communication protocol.
    - Gumblex: A resolver has been implemented.
    - Leo: Let's use `tar` files to transport build manifests, along with JSON and RESTful API?
        - Gumblex: Even `zip` files will work better (and easier for machines to read).
- Gumblex: Manifests will will also need to be signed. How should we go about doing this?
    - Leo: GPG?
        - Gumblex: This is probably unfriendly for machines and could therefore be difficult to implement.
    - Leo: Seems logical to use WireGuard-like key system for authentication?
        - Lion: I second this.
- Gumblex: Another thing worth noting is that builders may not have a public IP or port, so builders will actively communicate with and upload files to schedulers (the servers) which will probably have a public port.
- Neo: How should we measure performance for each builder?
    - Gumblex: Will use Glibc compilation as a standard benchmark.
        - Neo: But wouldn't this skew results across architectures?
            - Gumblex: They will be measured separately.
- Neo: How about builders with little RAM?
    - Gumblex: Builders will report available RAM before receiving jobs.