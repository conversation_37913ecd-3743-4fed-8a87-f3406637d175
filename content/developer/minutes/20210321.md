+++
title = "Contributor Minutes on 2021-03-21"
description = ""
date = 2021-03-21T22:28:55.386Z
[taxonomies]
tags = ["minutes"]
+++

Contributor Minutes on 2021-03-21
=================================

Attendees
---------

- Chenx97
- Cth451
- Cyanoxygen
- Fearyncess <PERSON>
- <PERSON>wy <PERSON>
- <PERSON><PERSON>
- <PERSON>
- <PERSON> Bai
- RedL0tus
- Staph
- Zixing Liu

Agenda
------

- [OSPP 2021: Community Registration](#ospp-2021-community-registration)
- [Survey Workflow Clearification](#survey-workflow-clearification)
- [Autobuild/ACBS/Ciel Progress Report](#autobuild-acbs-ciel-progress-report)

Minutes
-------

### OSPP 2021: Community Registration

- Bai: Let's dive into the details of the previously discussed OSPP projects. 
There is the localization project from me, then the RISC-V mainlining project from Icenowy, the installer GUI project from <PERSON>, and finally the second attempt at the packages rotation project from Staph.
    - Bai: About the project designs, can you explain how do you approach the project, <PERSON><PERSON>wy?
    - Icenowy: The scope of the project is currently unknown. I should be able to obtain the evaluation board by May this year.
    - <PERSON>: Earlier, you said that the goal is to roughly [adding] U-Boot and kernel peripherals supports. Is that correct?
    - Icenowy: Yes.
    - Bai: How about the student? Can they get their hands on one evaluation board then?
    - Icenowy: Probably, not guaranteed though.
- Bai: How about the you, Liu?
    - Liu: It depends. Although the difficulty is not that high. What's left for the student are: filling the holes in the backend implementation and then implement a GUI front-end.
    - Mag Mell: What holes?
    - Liu: There are some details in the backend implementations that are hardcoded or not flexible enough.
    - Mag Mell: Then is there anything in GTK3 that prevents our design to be implemented?
    - Liu: No, there was already a GTK3 design from before. Also, UI design is the product from compromises.
    - Bai: Cyan expressed his interest in this project earlier. Is it okay for a community member to participate in this project, Yhi?
    - Yhi: It's totally fine.
    - Bai: Another question, is `gettext` usable in this case?
    - Liu: Yes, you can also use Project Fluent.
    - Bai: You said Project Fluent is more difficult to use?
    - Liu: Right. Since Project Fluent is more advanced and more flexible.
    - Bai: Then we probably want to use `gettext` if we can. I can even take the opportunity to let the student from my project to translate this project.
    - Bai: Which difficulty level you would like to assign to this project?
    - Liu: Medium difficulty. It's not that hard.
- Bai: Okay, so will you explain the details of your project, Staph? More specifically, what this project will do and what are the differences from the previous attempt from the last year?
    - Staph: I have splitted the project into two halves last year. It was soon discovered that the difficulty distribution was not ideal. The student struggled during the first half and then I lost contact of them during the second half.
    - Staph: Since the Web UI is now finished, my project this year will skip this part. The project focus this year will be how to split the downloaded content [rotated packages] into 25 GB chunks [per directory].
    - Bai: So from my understanding, the UI is already there?
    - Staph: Yes, it was implemented last year. However, if the student is interested in [improving] the Web UI, they can do that too.
    - Bai: Like you said, the UI is completed. So the project this year will be finishing the splitting mechanism?
    - Staph: Well, the goal is to collect all the obsolete packages from the repository and then split them into 25 GB chunks and then collect some useful metadata from these packages. Also, deduplication is required.
    - Bai: Doing these work in a four-month period is probably too easy, I think?
    - Staph: The difficulty is designed to be easy. If they can finish the project in less than designed time, then good for them.
- Bai: For my project, the one dealing with localization, [...] firstly, we need to lay the blame on GNOME's door this year, since the [Chinese Simplified] localization completeness dropped to 96% shortly after the project completion last year. It also doesn't help that GNOME 40 significantly changed their UI.
On the other hand, I would like to improve the localization for KDE as well. The situation there is not sunshine and rainbow. The completion rate on stable is roughly 83% and trunk is 73%. Obviously, we are not aiming for 100% for KDE, a few projects will be handpicked for the student.
    - Bai: Also, I will adjust the difficulty to Medium this year to justify the workload.
    - Liu: Right, it [the difficulty] wasn't suitable last year.
- Cyan: I still want to participate in the installer project, although learning Rust is kinda hard.
    - Bai: You still have 3 months though.
    - Liu: Three months is totally enough.
- [.. some miscellaneous discussion omitted ..]
- Liu: If there are many candidates want to participate, there will be an examination to accept them.
    - Bai: From the previous year, we can see that this is really important.
    - Staph: However, if there is only one candidate, then it would be a pass-or-fail situation for the whole project.
- Bai: Alright. How about the OSPP online registration system they were bragging about?
    - Yhi: Nothing. [...] So we are going to fill out this registration form and email it to them.
    - Bai: Let's fill out this registration form right here and right now then.
- [.. discussions regarding filling out the forms omitted ..]


### Survey Workflow Clearification

- Bai: After moving to the topic-based iteration, we found that one topic for one package pattern is non-sustainable. So we introduced the survey mechanism.
A general survey is updating and rebuilding a batch of packages. For instance, updating Rust or Go. In which case, all the packages built using Rust or Go will be rebuilt or updated.
- Bai: For the workflow pattern, survey mechanism will be a exempt from certain [topic-based iterations] rules, where a large batch of packages may be accepted into one topic.
Also, on the subject of priorities, surveying topic will have the lowest priority as previously discussed with Liu.
- Bai: And when one of the affected packages in a survey topic is present in another topic, the other topic could be merged before the survey topic, and the latter needs to be rebased on top of the new stable branch.
- Bai: There is also the issue with pre-mature merging of topic branches, where the branch was merged before the packages were uploaded.
- Bai: I will come up with a set of Pull Request labels for our usage. I will explain this in depth next time.
Besides, hopefully I can set up a dedicated server for storing source packages this July. And then switch to use that server for packaging.
    - Mag Mell: It would be difficult to do that for packages like VSCode where there are source dependencies.
    - Bai: Liu said there are solutions to this problem: by bundling vendored sources.
    - Liu: Right, there are tools available for accomplishing this task.
    - Bai: We will be using AOSCNet for this transition.

### Autobuild/ACBS/Ciel Progress Report

- Bai: There's some progress being made for Autobuild/ACBS/Ciel.
I have linted all the `ABTYPE` build templates in Autobuild and now there should be less problems. 
And ACBS has added a safety check where if there is no package found after build, it will be reported as an error as well.
    - Bai: For architecture supports, there was a workaround implemented so that there should be less `NOLTO__LOONGSON3` present in the build scripts.
    Then I have implemented a basic QA system in Autobuild3 and it can detect some simple issues in the built packages, like linger files, incorrect permissions, etc.
    Lastly, cth415 has fixed an issue where static libraries are not correctly stripped.
- Bai: Liu, can you update us on the progress of ACBS/CIEL?
    - Liu: Right. I have modified ACBS so that it will detect `FAIL_ARCH` before build now, saving some time. Another change is that it uses `libapt-pkg` to query APT states.
    - Liu: On the topic of automation, there are only two people on the job: Leo and myself. Considering this is a larger and complicated project, it will take a while. Currently, I splitted the work into schduler and agent like our last attempt.
    - Liu: However, learning from the previous mistakes, we are currently doing this from the smallest component possible first. So even if it failed again, there will be salvagable parts. And when doing this project, we can build the project like assembling puzzle pieces.
    - Liu: There will be modifications made to Ciel so that it can be controlled programatically. And the components made for scheduler is nearly finished.
    - Liu: [.. Very detailed descriptions omitted ..]


Actionables
-----------

- Everyone: Reconvene in the next week.
- Junde Yhi: Submit OSPP registration forms.
- Mingcong Bai & Zixing Liu & Staph & Icenowy: Prepare for the project proposals. 
