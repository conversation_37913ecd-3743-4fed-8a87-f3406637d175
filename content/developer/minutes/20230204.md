+++
title = "Contributor Minutes on 2023-02-05"
description = ""
date = 2023-02-05
[taxonomies]
tags = ["minutes"]
+++

Attendees
---------

- Canary
- <PERSON><PERSON>
- <PERSON><PERSON><PERSON> (Bai)
- <PERSON><PERSON> (Sa<PERSON>)
- <PERSON><PERSON><PERSON> (Liu)

Agenda
======

- [AOSCPT Demo and Review](#aoscpt-demo-and-review)
- [AOSC OS Desktop Survey 20221225 Review](#aosc-os-desktop-survey-20221225-review)

AOSCPT Demo and Review
======================

The following items are to be addressed in future AOSCPT updates.

- Add an option to refresh repository data (`oma refresh`), whereas previously, `oma update` would refresh said data and update the system in one go.
    - Rename `oma update` to `oma upgrade` to avoid confusion by APT users, add alias for this command.
    - `oma upgrade` to still refresh repository before attempting a system update.
- Add parallelism to download operations.
    - Use MultiProgress (used by original Omakase) to display progress.
- Display a more useful error when dpkg fails to configure updates.
- Allow a normal user to initiate management functions (update/install/remove) upon PolicyKit authentication.
    - Refuse to execute if user is not a system administrator.
    - Allow non-administrator users to install Flatpak?
- Attempt to implement FlatHub search and installation/removal/update support.
- Implement a fallback search UI for non-matching searches.
    - Mock-up as follows.

```
# oma install qq

Found the following matching packages from the community repository:

qqmusic v3.2.21-1 [amd64]
[DESCRIPTION BLOCK]

Found the following matching packages from FlatHub:

com.tencent.QQ v114.514
[DESCRIPTION BLOCK]

com.tencent.QQmusic v3.2.22
[DESCRIPTION BLOCK]
```

AOSC OS Desktop Survey 20221225 Review
======================================

Reviewed the `desktop-base-survey-20221225` topic, which updates KDE and various pre-installed desktop applications.

- Issues to be addressed (The Bad).
    - Application: Replace Skanpage (`skanpage`) with the more usable Simple Scan (`simple-scan`).
    - Elisa: Enable RPATH to avoid shared object search failure.
    - Fcitx5: Rename "Keyboard Layout Viewer" as "Keyboard Layout Tester," **contact upstream.**
    - Firefox: Is not the default browser when it's the only browser installed.
    - Firefox: Text files are opened by LibreOffice by default, should use KWrite instead.
    - Firefox: When setting the browser as default in `about:preferences`, the Firefox icon disappears and re-appears as a blank placeholder on the panel and launcher favorites.
    - GParted: Survey file system tools dependencies.
    - Gwenview: Enable normal menu bar by deafult.
    - Kdenlive: Missing `ffplay`, `mediainfo`, and MLT modules.
    - Panel: Disable task manager grouping.
    - Settings: Change default scaling method to "smooth" instead of "crisp" in Compositor settings.
    - Settings: Default Monospace not set to `Source Code Pro`, as is the case with Konsole.
    - Settings: Disable Baloo by default.
    - Settings: Disable maximise/restore and pop-up size transition animations.
    - Settings: Fails to install Plymouth themes, **contact upstream.**
    - Settings: Remove "Online Accounts" settings.
    - Settings: Removing SDDM themes installed from KNewStuff (Pling) will result in a lock-up in KNewStuff if "Get new themes..." is clicked on again, as the KNewStuff window fails to refresh package installation status.
    - Settings: System language not applied due to "font installation failure," and UI does not prompt users to reboot to apply settings.
    - Spectacle: Add SimpleScreenRecorder (`simplescreenrecorder`) as dependency for screen recording.
    - Spectacle: Discover fails to find alternative applications needed for screen recording (AppStream data issue?).
    - Taisei: Nanny should test for OpenGL support level and handle non-GUI errors.
    - Wallpapers: File name displayed instead of titles.
    - Xprofile: Enable ScrollLock by default.
- For future reference (The Uncertain)
    - Desktop: Should icons be enabled by default?
    - OOBE: Include a first-boot wizard (Plasma 5.27 feature)?
