+++
title = "Contributor Minutes on 2020-04-19"
description = ""
date = 2020-05-04T03:36:31.287Z
[taxonomies]
tags = ["minutes", "dev-automation", "dev-sys"]
+++

## Gumblex

- drawn a diagram
- downloader
- scheduler demo, using 线性规划
- 想梳理包的关系，但是打环很严重，做不出来，比较困难，暂时没有继续做
- 节省安装依赖的时间：GTK3, Qt5？
  - 是引起打环的大头
  - 原本想计算，但计算方法很难求很好的解
  - 希望以这两个包作为缓存机制
- 目前 Ciel 无法进行多级镜像管理
- 需要把测试和打包的环境分开
- RedHat builda / podman
  - Builda 用于打分层的镜像包
  - 用 builda 代替目前的 ciel 的用途
- 不使用 UID Namespace
- 在元数据中规定一个 Bash 子集，方便 Parse

## Lion

- 包的依赖关系确实非常复杂
  - 环，很大（意料之外）
  - 包之间有复杂的 Conflict, Replace, Rename 关系
  - 意图不同
  - 对用户来说没有太大代价（整个环安装上）
  - 对打包的问题：存在依赖问题，但并不会造成很大问题
    - 已经有办法探测出来
  - 安装包，更大的问题在 IO 上
    - 在做 ACID 的时候，监控发现 CPU 占用有时候很低，两个推测：
      - IO
      - Postinst
  - builda 有 UID Namespace (fakeroot like)
    - 操作起来有点困难
    - 安全性问题存在，但危害不明显，主要希望提高效率
    - 甚至可以不开，带来了很多不必要的麻烦
- 目前维护工作流上有一些麻烦
  - 编辑脚本重复工作较多
  - `aeditor nano update 2.0`
    - liushuyu: `abed`? `abcd`?
- ab3 有多少 Bash 语法？
  - spec version 处理
  - 没有 for, do, case
  - 比较积极的信号，可以做性能提升
  - 重写条件令解析更好做
- Downloader 已经解决了很大一部分的 spec 问题
  - 上游更改，……
  - 消灭了很多的 spec 内字符串替换
- **可以尽快制定 spec 使用的 Bash 语法子集**
  - 放进 wiki
- 版本比较器测试非常完备
- **可以尝试在包内 (control) 放置构建时信息**
