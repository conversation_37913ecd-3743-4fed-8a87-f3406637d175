+++
title = "Contributor Minutes on 2023-12-16"
description = ""
date = 2023-12-16
[taxonomies]
tags = ["minutes"]
+++

Topics
======

Today's meeting focuses on various project planning and updates.

- [下一代 Autobuild 愿景](#xia-yi-dai-autobuild-yuan-jing)

下一代 Autobuild 愿景
===

简述
---

Autobuild3 和 ACBS 并项：实际上让 Autobuild3 实现了树和源码管理功能；继续提供两个命令保持兼容

- 暂定命名：Autobuild4
- 预期工时：一个月，明年年初投产测试

主要实现目标
---

按重要性排序：

1. 错误控制改善：新实现用 C 或 C++ 实现，但使用 Bash 插件（重实现一部分内建功能），进而可以读取 Bash 内部状态，更可靠地捕捉状态（尤其是语法解析错误，这一类错误先前不会中止运行）
2. 源码变种支持：完整实现 `SRCS__${ARCH}`（或 `__${ARCHGROUP}` 对应的 `CHKSUMS__${ARCH}`（或 `__${ARCHGROUP}` 回写功能
3. 完整依赖关系标记实现：如 `<<`、`<=` 和 `==` 等
4. 进一步抽象模板：某些软件包只需要用模板的一部分，或需要多次使用模板——这些情况在当前实现中需要自定义脚本，重新实现与模板相似的逻辑，造成质量保障风险；`build` 脚本中亦可使用模板（类似 RPM `.spec` 文件中引用的 macro，如 `%configure` 和 `%make` 等）
    1. 保持与当前实现兼容，可后续迁移
    2. 将使用模板作为软件包风格要求，但不默认报错，构建成功后提示使用的模板，要求打包者填入 `defines` 文件（将显式指定模板作为风格要求，在审阅时检查）
    3. 各类 optenv 及变种包模板，作为内置模板中的选项简化实现过程
5. 词缀系统：允许流程性地开启或关闭某些参数，如 `PKGBREAK`，使得构建某些需重构的基础软件包时无需手动 hack 构建配置，实现完成流程化
6. 迁移参数值为数组：以防参数内空格无法正确转译的问题（目前 `autotools` 和 `cmake{,ninja}` 模板已支持该功能），新实现中将绝大多数参数作为数组处理，并引入简易类型检查（包括 `CFLAGS` 等自定义参数）
    1. 短期内保持兼容，并将数组标记作为审阅检查项目之一
    2. 引入专用函数用于删除数组项，这一功能将明显简化 `CFLAGS`/`LDFLAGS` 等变量调整的流程
    3. 引入新 QA 检查，报错并推荐正确类型
    4. 有限度地自动修复错误（默认不打开该功能，允许用户自行打开；aosc-os-abbs 流程中不允许这类操作）
7. 改善调试符号保存逻辑：某些上游构建配置中会在构建时 strip 二进制并生成相应调试用文件；新工具允许打包者手动复制上游分离的调试符号到固定位置并自动归类打包
    1. 并行化 strip，提升效率（尤其是 LibreOffice 这类文件非常多的包）
    2. 允许在某些架构上使用特定的 strip 工具（如使用 elfutils 中的 `eu-strip` 作为备选；RISC-V 曾经有无法正常分离符号的问题）；如仍无法保存则列作错误处理
    3. 默认使用 elfutils，可正常保存 Rust 等包的调试符号
8. 集成 [Pixie](https://github.com/AOSC-Dev/pixie) ELF/Python/语言包依赖解析器，将 `BUILDDEP` 作为构建环境组成标准
    1. 可能需要将 elfutils 作为依赖，stage2 可以不打开该功能（或 bundle elfutils）
9. 泛 Debian 化：集成 [spiral-rs](https://github.com/spiral-repo/spiral-rs)，按照 Debian 软件包命名标准，默认回写 `PKGPROV` 项，使得每个系统组件都能提供相应的 Debian 依赖包，提升系统兼容性
10. 导出根据 Bash 逻辑解析后的值：方便 abbs-meta 等外部工具直接读取软件包构建配置
11. 允许低权限运行：可行否？（非关键特性）
    1. 考虑到 Core 12 准备引入多优化级别支持，本来计划中的本机构建优化后台服务可以考虑不实现
    2. 低权限运行的一大好处：可以抓到构建时需要修改系统环境的包，并提前报错
12. 界面本地化

迁移规划
---

基本点：保持兼容，允许中长期迁移
