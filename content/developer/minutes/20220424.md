+++
title = "Contributor Minutes on 2022-04-24"
description = ""
date = 2022-04-24
[taxonomies]
tags = ["minutes"]
+++

Contributor Minutes on 2022-04-24
=================================

Attendees
---------
- <PERSON><PERSON> (Saki)
- <PERSON><PERSON><PERSON> (Bai)
- RedL0tus
- <PERSON><PERSON><PERSON> (Liu)
- Icenowy
- OriginCode
- cth451
- chenx97
- <PERSON><PERSON>
======

- [OSPP 2022 Preparations (3)](#ospp-2022-preparations-3)
- [Other Distro Review (1)](#other-distro-review-1)

OSPP 2022 Preparations (3)
=======================================
- Bai: Unfortunately we only got two slots for the OSPP projects this year. And it is non-negotitable. So what projects should we submit?
    - Liu: This depends on what direction we focus on. If we focus on the probabilities of the projects passing final evaluation, I'd say Localization project and the Wiki project. If we focus on the probabilities of the projects passing OSPP committee's initial evalution, then I'd say Packages site re-implementation and Allwinner RISC-V chip Mainline Development.
    - Bai: Hmm tough choice. Do anyone in this channel has any thoughts on this?
    - Bai: If we focus on the resume direction, then Port AOSC OS to LoongArch Architecture and/or Wiki project.
    - Liu: For the students' perspective, the most attractive would be the middle three in the list.
    - Liu: However, Packages site re-implementation and Allwinner mainline project are the most difficult to attract the students. Considering the current impasse, let's just open a vote on this matter.
    - Bai: Okay, let's do this.
- SYSTEM: Vote result:
    - Round 1: (1) Packages site re-implementation; (2) Port AOSC OS to LoongArch Architecture; (2) Allwinner RISC-V chip Mainline Development; (2) Localization; (5) Wiki
    - SYSTEM: Tie-breaking required. Round 2 starts.
    - Round 2: (1) Allwinner RISC-V chip Mainline Development; (2) Localization; (3) Port AOSC OS to LoongArch Architecture
    - SYSTEM: Selecting: Packages site re-implementation and Allwinner RISC-V chip Mainline Development
- Bai: Okay, then let's just submit the projects accordingly.
- *Bai submitted projects*
- Bai: Next agenda, please.

Other Distro Review (1)
=======================================
- Bai: The distro to review this week was voted to be Manjaro KDE.
    - Saki: Okay, I have launched a virtual machine with the installation medium.
- *Review starts, detailed discussion omitted*
- Bai: So in conclusion, we do have something to learn from this distribution: they have a news aggregator for the community news, they also comes with a backup program pre-installed, called "Timeshift", and they have an utility to install GPU drivers.
    - Saki: They also have a cool terminal prompt. However, we have also have our own bash theme.
    - Bai: Speaking of backup programs, we can offer a watered-down version in the RescueKit in the future to backup home directory and/or system directories if desired.
    - Bai: Also, we need to take some time to clean up the application menus.
- Bai: To recap, to apply those advantages to our own distribution, we need to: (1) make a news aggregator program; (2) integrate a backup program; (3) make a GPU driver installation script; (4) polish the shell prompt theme; (5) investigate whether ONLYOFFICE could be an alternative to the LibreOffice.

Actionables
===========
TBD

Addendum
===========
Candidate Distributions to Review Each Week:

- Deepin
- Ubuntu
- Pop_OS!
- Linux Mint
- MX Linux
- Ubuntu MATE
- Kubuntu
- Alt Linux
- Adelie Linux
- PCLinuxOS
- EndeavourOS
- ZorinOS
- openSUSE
- elementaryOS
- Ubuntu Kylin
- UOS
- Solus Linux
