+++
title = "Contributor Minutes on 2022-08-28"
description = ""
date = 2022-08-28
[taxonomies]
tags = ["minutes"]
+++

Contributor Minutes on 2022-08-28
=================================

Attendees
---------

- <PERSON><PERSON>
<PERSON><PERSON> (Canary)
- chez
- <PERSON><PERSON>
- <PERSON><PERSON><PERSON> (<PERSON>)
- <PERSON><PERSON><PERSON> (Icenowy)
- <PERSON><PERSON> (Saki)
- <PERSON><PERSON><PERSON> (Bai)
- <PERSON><PERSON><PERSON> (Liu)

Agenda
======

- [OSPP Progress Report](#ospp-progress-report)
- [AOSCC 2022 Planning](#aoscc-2022-planning)
- [AOSC OS Desktop Self-Review](#aosc-os-desktop-self-review)

OSPP Progress Report
====================

(Chez demonstrates progress on his now finished Packages site...)

- <PERSON>: Oops, seems like you didn't colour the versions for the "Latest Source Updates" section on the home page.
    - chez: I will add that later.
- Bai: Perhaps add the permalinks to error lines in defines.
    - chez: Got it, I will add it - shouldn't be too hard.
- <PERSON>: Also consider removing the "PISS" version upgrade indicator in package detail pages. We don't really use this in our workflow and this makes our backend design more complicated than necessary.
- <PERSON>: I also have doubts about where the QA link currently is. It's a little bit confusing.
    - chez: Would I need to make major changes?
        - Liu: Unlikely, since we are close to the end of your term. Just something to think about.
- chez: Next steps would be to finalise the frontend design, add README and unit tests to my repo.
    - Bai: And don't forget about license and documentation.
    - Liu: Consider using [cargo-about](https://github.com/EmbarkStudios/cargo-about) for advice on which license to go with - based upon your dependency list in Cargo.toml.
    - chez: Any requirements for which license to go with?
        - Liu: No, but if you see LGPL/GPL in any of your dependencies, you might need to use one of the two.
        - Saki: We usually use GPLv2 for our projects.
            - Liu: Right, but for future convenience, it's best to go with something like "GPLv2 or later," as some of the dependencies will be using it anyways.

(On Icenowy's situation...)

- Bai: Icenowy, we have heard bad news from your students, and previously your student indicated that he wanted one more chance. How are we doing with that?
    - Icenowy: I'll just wait until the project ends but I reserve my right to fail his project.
        - Liu: Okay, just make sure to communicate what you deem as passable.

AOSCC 2022 Planning
===================

- Bai: AOSCC scheduled in Mid-september to make time for new OS release. The new OS release (hopefully) will be easier to install as the DeployKit is now in a good shape. Retro distribution had some usability configuration changes as well.
    - AOSCC will only occupy one day this year, as in the last year, the second day was reserved only for chit-chat.
    - Several new projects to discuss: DeployKit GUI, and CambeR's Autobuild3 testing framework. Some new desktop utilities like news flash.
    - Some controversal topics: Core 10 tech-stack; whether autobuild3 plainmake type should go; how far should we enforce the QA requirements.

Before Mid-September

- More documentations on packaging guidelines and behaviors. This is to attract new developers and new users.
- We had the AOSCC last 2 years online on Discord, and gained much experience on live-stream tech. Issues with stickers: SaltedFish has some family business to take care of, progress hindered, contributions welcome.
- One week prior to AOSCC, September, 10th, more detailed planning will be available. camber, saki, liushuyu need to prepare materials and speaker notes in advanced. OSPP mentors should prepare too.

AOSC OS Desktop Self-Review
===========================

- Bai: Now that I have a working Desktop configuration copy, let's take a look at our own Desktop distro. Let's review in detail as we did with other distros in the past few months.
- Bai: I'd like to change the KSplash, it's just too ugly.
    - Saki: Yeah, this needs to change.
    - Bai: I'll try and replicate a Windows-like seamless splash using the SDDM background.
- Saki: I think we should just go with KSysGuard, the new system monitor is just too slow.
    - Bai: Reasonable advice, since we are prioritising responsiveness with this new design.
- Icenowy: We should consider condensing the family of fonts we ship by default.
    - Bai: Why?
        - Icenowy: It really does slow down LibreOffice's whole UI.
            - Saki: But I don't think this is unreasonable as it is.
        - Icenowy: We have already patched Fontconfig for this, we have paid quite the price for the fonts we preinstall.
            - Liu: Other distros also ship full Noto sets.
            - Bai: Ditto. Distros apart from Debian/Ubuntu rarely split their Noto font package.
    - Liu: Also, I only allocated two cores and 2GiB of RAM to this VM. I think this is okay actually.
    - Lily: We should also consider splitting out different font weights and styles (Debian has a `noto-fonts-extra` to provide extra font weight, the standard font provides only Regular, Bold, and Italic).
        - Bai: But this is undoable? Fonts are already shipped as one file...
            - Icenowy: No, we use TTF.
        - Saki: I think this is pointless, you'd need those font weights anyways. If a user chooses to install all weights it just brings us full circle, no?
    - Icenowy: One bottom line, I think Noto should be used to keep Unicode coverage, but not as the main typesetting font.
        - Bai: But Noto is quite a good typesetting font?
            - Saki: Yeah, I don't know what this suggestion is good for, honestly.
    - Bai: But let's consider this - if we are going for an "acceptable" experience, why not go with WenQuanYi, Libreration? Those should suffice? But is this really what we are going for?
        - Icenowy: But either way, I think our Noto font distribution is quite unreasonable. Split fonts by langauge and weight is the way to go.
    - Icenowy: But I think a Fontconfig update is something to look into, it seems that from Ubuntu Bugzilla, an update to 2.14 can improve performance.
        - Bai: Yeah, I think this should be the essential method. If this were Retro I'd strip fonts out quickly.
    - Liu: I think to close out this discussion, for system UIs, it's difficult to gauge what's necessary or what's not. Even from within the LibreOffice suite, when it comes to Impress, the font appearance requirement becomes more dire... But either way, let's take another look at other distributions to see what's what. I think it's not completely out of question that there are some performance issue on our part.
        - Bai: Yes, and beyond performance and selection, subjective views needs to be a question of democratic decision making.
- Saki, Icenowy: KDE Connect URL handler .desktop entry could be stripped.
- Icenowy: Fcitx entry should also be removed since it's already XDG autostart anyways.
- Icenowy: Add Pidgin to Web?
    - Bai: Any objections or better ideas?
        - Saki: HexChat? Konversation? Something more modern?
            - Bai, Liu: There's nothing better than Pidgin, really, even if it's GTK+ 2.
- Icenowy, Liu, Saki: Keep Qt tools in Development but hide all GTK+ demo applications.
    - Icenowy: Maybe keep the icon browser?
        - Bai: Let's go with the KDE icon browser and leave this out hidden.
- Bai: Strip Akonadi from Desktop.
- Bai: Let's split out the Fcitx 5 migration wizard?
    - Saki: Yes.
    - Icenowy: Can we?
        - Bai: Yep, easy.
- Bai: Okular shouldn't be in Graphics, neither should Skanpage (should be in Office).
    - Bai: But we can add Kolourpaint, Kcolorchooser.
    - Liu: Also consider adding Skantailor.
- Saki: Maybe the crash viewer should be in Development?
    - Icenowy: No, this is a system tool.
- Icenowy: We should also strip out the .desktop to the Web interface.
- Saki: Drop PartitionManager?
    - Bai: Replace with GParted?
        - Saki: Yes, it's much better.
            - Icenowy: Seconded.
- Saki: Should Konsole be in Tools?
    - Icenowy: I think we should stick with upstream. When in doubt, stick with upstream.
- Bai: Kamoso should be in Utility.
    - Saki: Seconded.
- Icenowy: We could also look into Maui applications. Also based on Kirigami, a fork, rather, but there are some good ones there.
- Saki: Let's get rid of Strawberry's notification pop-up.
    - Liu: But we should ask for a "only notify when minimised" version.
- Saki: We also need an AOSC OS specific KCM for system and support details.
    - Bai: And also an integrated chapter in KHelpCenter.
- Liu: Oops, KHelpCenter cannot display any Unix man pages.
- Saki: Maybe consider adding a few KDE games.
    - Bai: Or maybe something better.
- Bai, Cyan, Liu, Saki: Add Remmina as well for remote connection, also an audio recorder, kcharselect for character selection.

Actionables
===========

- Liu: Communicate package site design changes with chez.
- Everyone: Prepare for AOSCC.
- Bai: Implement feedback in upcoming AOSC OS Desktop release.
