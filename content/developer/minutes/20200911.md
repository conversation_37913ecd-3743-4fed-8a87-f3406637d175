+++
title = "Contributor Minutes on 2020-09-11"
description = ""
date = 2020-09-11T23:03:55.386Z
[taxonomies]
tags = ["minutes"]
+++

Contributor Minutes on 2020-09-11
---------------------------------

Attendees
---------

- Zixing <PERSON>
- <PERSON><PERSON><PERSON>
- <PERSON>
- chenx97
- <PERSON><PERSON>
------
- [Packages Site Renovation](#packages-site-renovation)
- [Returning to Routine Meetings](#returning-to-routine-meetings)
- [Planning for Shortened AOSCC 2020](#planning-for-shortened-aoscc-2020)

Minutes
-------

### Packages Site Renovation

- Bai: The packages site currently need some adjustments, there are some missing branches like `stable` and `testing`
    - Gumblex: If you don't push any packages to these branches, then you can't get any out of them.
- <PERSON>: Also, I noticed just now that there are two `ppc64el` shown on the packages site.
    - Gumblex: That listing was implemented on the frontend.
- <PERSON>: There is also an issue with the `p-vector` where if you put `retro` variant with the `mainline` variant, then the `p-vector` will repeatedly reset the database and re-create them.
    - Chai: How so?
    - <PERSON>: Did you remember before you set up the `retro` variant instance, the `p-vector` was stuck in the loop where it was re-scanning all the packages non-stop.
    - Chai: Oh I see.
    - Liu: (To Gumblex) Do you have any idea about this issue?
    - Gumblex: I didn't get to see the issue myself, so I can't deduce what is wrong with it. How about just make two separate package sites?
    - Liu: Well you can, although I think you can probably make one instance but connecting to two databases at the same time if that is possible.
- chenx97: Did you remember the issue with the remote SQL invokations?
    - Liu: Packages site usually will show HTTP 5xx errors because of this.
    - Gumblex: It's usually the server can't connect to the database. Also do you mean the HTTP 500 errors on the packages site or just the QA pages?
    - Bai: They all might return the errors.
    - chenx97: Everything database related can exhibit this issue.
    - Liu: I recommend to use hot-standby mechanism to archieve single-directional synchronization. I guess the packages site itself may not write any data to the database, so creating a local replica on the packages site server should resolve this issue. What do you think?
    - Gumblex: It should work, but seems daunting.
    - Liu: I can do that for you, the latency shouldn't be too high. Also comparing this latency to the scanning lag on the repo server, it doesn't really matter. Also, confirming this one last time, is there any write operations on the packages site?
    - Gumblex: No.
- Bai: (To Gumblex) Did you get my concern about the branch statistics?
    - Gumblex: Which one?
    - Bai: I want to change (this part) to show the overall count of all the packages instead of the differential count. Is this difficult to change?
    - Liu: I guess Gumblex needs to consult the source code which you may haven't seen it for years.
- Bai: Also a low-priority item: re-style the packages site to be more in-line with the main website.
    - chenx97: Maybe the (new) [wiki site](https://wiki-aosc.netlify.app) is in a more similar situation.
- Liu: Speaking of database replication, I did that for another open-source project. It was a headache to do but that was a MySQL situation. You need to first dump the full data tables from the master and then import the dump to the replica.
    - Gumblex: However, as you can see (* shows the screenshot *), my server does not have enough disk space to accommendate the whole database.
    - Liu: Whoa. The `files` table is weighting at 16 GB. Do you remove old data from this table?
    - Gumblex: Yes, as you can see from the source code, the clean-up operations will be performed after the scan.
    - Liu: Okay, in this case, we may find a way to partially replicate the database. Just leave the `files` table behind. I think it's not used very often, no?
    - Gumblex: No. It's only used in the file listing page.
    - Liu: One other thing to note is that the replica will have (nearly) exactly the same data as the master including the credentials.
    - Gumblex: That one's a thinker...
    - Liu: Well, you can always just spin-up another PostgreSQL instance. The current system used a TLS connection to connect to the repo server?
    - Gumblex: No, it is a SSH-tunnel.
    - Liu: Well, there's your "high" performance. I think the replica idea is better since if the repo server happened to be down at some point the packages site can still function for quite a while. As it was designed to be used as a fail-over mechanism.
- Liu: I think that is everything for this topic.

### Returning to Routine Meetings

- Bai: Let's return to a routine schedule for our meetings. Apologies for not holding any for the past few months, less desirable things in life got in the way. How does every Saturday at 10:00AM UTC+8 sound to you?
    - Liu: Sounds good.
- Bai: And especially for Gumblex, would this time work?
    - Gumblex: Yes, I can work with that.

### Planning for Shortened AOSCC 2020

- Bai: This isn't much of a discussion than a quick update. Let's still do an AOSCC this year, but shorten it, and focus mostly on reports. We've been working on some major projects, namely bringing up AOSC OS/Retro and Autobuild3/ACBS/Ciel reworks, so let's talk about those.
- Bai: Other than that, there's one major change to discuss this year, as Icenowy Zheng brought up a few weeks back, we should move to a topic-based maintenance cycle. This means that we should plan our merges by "topics," i.e. Boost update and rebuilds, KDE Application updates.
    - Liu: That actually sounds like a good idea.
- Bai: I was thinking about the weekend of September 26 - 27th, that would be three weeks out. We could prepare our reports and notify the community so that they might join in with package maintenance training, and things like that.
    - Gumblex: That would work.
- Bai: Well, why don't I draft up a schedule in the next few days and let's go from there.

Actionables
-----------

- Gumblex: Packages site renovation.
- Liu: Help Gumblex with PostgreSQL hot-standby setup.
- Bai: Write preliminary plan for AOSCC 2020.
