+++
title = "Contributor Minutes on 2021-02-07"
description = ""
date = 2021-02-07T12:28:55.386Z
[taxonomies]
tags = ["minutes"]
+++

Contributor Minutes on 2021-02-07
=================================

Attendees
---------

- Chenx97
- <PERSON><PERSON><PERSON>
- <PERSON>
- <PERSON>aph
- Zixing Liu

Agenda
------

- [OSPP 2021: Project Brainstorm](#ospp-2021-project-brainstorm)
- [OSPP 2021: Feedback](#ospp-2021-feedback)
- [OSPP 2021: Other Remarks](#ospp-2021-other-remarks)

Minutes
-------

### OSPP 2021: Project Brainstorm

- Bai: Let's start with an introduction of the OSPP, since we have new members since 2020. Then, let's move on to talk about some of the feedback for our involvement last time, and brainstorm some ideas.
- <PERSON>hi: Not much change in terms of processes. We need to look at "Community Guide" for now, in order to sign up.
- Bai: What do we need to prepare at this point?
    - <PERSON>hi: We are not yet at the stage of signing up, let's start by talking about some of our ideas.
- <PERSON>: Could we organise a few localisation projects, with different mentors?
    - Yhi: This could be difficult, there is no precedent, and this is probably against the rules. One mentor could guide a few projects at a time.
- Icenowy: I think there is a possibility - mainlining an [Allwinner RISC-V](https://www.cnx-software.com/2020/11/09/xuantie-c906-based-allwinner-risc-v-processor-to-power-12-linux-sbcs/) SoC for various SBCs. This is not yet a public product.
    - Bai: Would we be on time to have it disclosed for April?
    - Yhi: This is fine, if NDA is expired.
    - Bai: What does this entail?
    - Icenowy: Mostly U-Boot and Kernel peripheries. This would not be suitable for a newcomer.
    - Bai: This is probably a high difficulty project?
    - Liu: I think so.
- Bai: I had an idea to port a complete Raspberry "userland" support for AArch64 (especially the MMAL components), but would this be too difficult?
    - Liu: Any blob or reversed engineering needed?
    - Bai: None, as far as I remember.
    - Icenowy: If source code is available this shouldn't be impossible, but still probably a "high difficulty" project.
    - Liu: But isn't MMAL proprietary?
    - Yhi: I think it's best if we put it off? We need to be sure about the project's doability.
    - Icenowy: At this point, I think the problem is really that the VC4 is 32-bit, while the processor is 64-bit.
    - Bai: It's also unclear if the upstream wants to address this issue.
    - Liu: Also unsure if they would accept our code.
    - Fearyness, This issue is already addressed with userland 1.5.2. This is actually a non-issue.
- Staph: I would probably retry the archival project, since it shouldn't be too bad. The student last year was simply unacceptable.
    - Liu: What was the difficulty given for it?
    - Staph: Low. The student had no basics. I was hoping more student would sign up, and didn't think that the students would have any difficulty doing it. At the same time, I probably shouldn't raise the difficulty either.
    - Yhi: This is probably just luck. We could try again.
    - Bai: So your thought is to reuse materials from last week?
    - Staph: In principle, yes. But I should probably try and improve our outreaching.
    - Bai: Yeah, I think the students might be confused about what the project is about.
- Saki: I would like to see if anyone is interested in making a vertical mode for Fctix candidate windows.
    - Liu: This project is probably too small, especially for four months.
    - Yhi: We should keep in mind that the projects should last more than two months.
    - Saki: Hmm, then I'm not sure...
    - Yhi: It's best if the project does not present itself as something possible to be finished in a week or so.
    - Bai: You should also think about how involved the project really would be.
- Saki: How about a GUI-driven installer?
    - Liu: I think this might be too demanding, since the installer GUI is planned to use a game engine-like toolkit, which is OpenGL heavy.
    - Bai: I think in principle, if we set it as a high difficulty project, this could be doable?
    - Yhi: Even if we set an "OpenGL" tag for the project, we should also be able to meet capable students, since OSPP is promoted in many 985 institutions. Liu, did you leave an interface for GUI expansion?
    - Liu: Of course I did.
    - Yhi: Then you could probably try and set one up.
    - Liu: But the AOINB project failed miserably.
    - Yhi: I still think you should present the project, since you are probably capable of mentoring a student.
    - Bai: I think we could discuss UX before OSPP, and leave the student to implement it.
    - Liu: This could be easier, in fact, if we use something along the line of Electron.
    - Cyan: We could probably try GTK+ and Qt instead, but would Rust get in the way?
    - Liu: We are good, since there are bindings available.
    - Yhi: Yeah, let's do it. Liu, do you want to do it?
    - Liu: I still need to do automation, which I'm likely to be a mentor for it.
        - Yhi: I think we could probably leave automation this year.
        - Bai: I would also think installer is probably more practical.
    - Bai: So what do you say? We don't need to decide today.
        - Liu: I think you all could decide too.
            - Yhi: I think you would need to decide as a mentor.
        - Bai: I think you could decide on which one of the two, or neither, and see what approach we take toward this.
            - Liu: Since we have four months, if the student is capable enough, one month should be enough.
                - Yhi: This is good.
    - Liu: OpenGL could minimalise dependencies, but could be problematic for GL-less devices. We could probably look at other options too.
- Fearynces: I wouldn't participate this year, and I have got a job this week. Also, I'm busy with Phytium, and I'm a beginner on this as well.

### OSPP 2021: Feedback

- Bai: In terms of our participating experience. I would like to voice my concern regarding their fund remittance process, which required us to disclose bank information on GitLab.
    - Yhi: They have probably addressed it this year.
- Yhi: We should also wait until March until we sign up, since they will probably have set up an sign-up form already.
- Bai: Any other concern that we should take note on at this point, Yhi?
    - Yhi: No.
- Staph: I have another point about continued participation. All students last year did not stay in the community to continue contribution.
    - Saki: Also that our OSPP project channels was not effectively integrated with our community channels. I think they should probably just contact us via our main groups.
        - Staph: Indeed. Some of them also had issues with VPN and Telegram.
        - Bai: We should probably take note on this.
- Staph: Should we provide VPN for our students? My student last year did not know how to gain access to international Internet.
    - Yhi: This should be considered an indicator for student skills.
    - Staph: But how should we let the students know? We probably shouldn't publicise this as a requirement?
        - Saki: We need to do it in private.
- Yhi: They have set up a questionnaire for our feedback, *reads a few questions*.
    - Bai: They all sound pointless.
    - Liu: Just formalities, I guess.
        - Yhi: Yep, just as a sign up requirement.

### OSPP 2021: Other Remarks

- Bai: let's not get into too much detail at this point, since we are still a month away. But for now, let's think about how the sign up would go, and how we should address the issue about information exchange with the community at large.
    - Yhi: My suggestion is to stay with Telegram, and encourage students to use it. If this isn't doable, then we could fallback at the mentor's discretion.
    - Bai: Given my student last year checks Telegram only infrequently, we should probably enforce stricter rule on communication.
- Bai: When should we meet next?
    - Yhi: Let's reconvene again when their sign up system goes live.
        - Bai: When? Could you follow up with them?
        - Yhi: I'm not sure if they've got everything figured out, but I will follow up with them.

Actionables
-----------

- Everyone: Reconvene in early- or mid-March with more details.
- Junde Yhi: Follow up with OSPP regarding community feedback.
- Zixing Liu: Think about how to approach installer, or automation project.
