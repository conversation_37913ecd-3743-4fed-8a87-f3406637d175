+++
title = "Contributor Minutes on 2021-07-04"
description = ""
date = 2021-07-04
[taxonomies]
tags = ["minutes"]
+++

Contributor Minutes on 2021-07-04
=================================

Attendees
---------

- cth451
- Cyanoxygen (Cyan)
- Fear<PERSON>ess
- <PERSON><PERSON><PERSON> (Icenowy)
- <PERSON><PERSON><PERSON> (Liushuyu)
- <PERSON><PERSON> (Saki)
- <PERSON><PERSON><PERSON> (Bai)
- RedL0tus (TheSaltedFish)
- szclsya (Leo)
- <PERSON><PERSON>
- 没有字的回音 (WordlessEcho)

Agenda
------

- [Core 9 Feature Planning and Discussion](#)
- [PineBook Pro Issue Reporting with <PERSON><PERSON><PERSON>](#)
- [OSPP 2021 Student Progress Report](#)

Core 9 Feature Planning and Discussion
--------------------------------------

- Bai: Let's talk about Core 9. I don't have much planned apart from general updates, but a suggestion to move to `-mtune=intel` (whereas we had `-mtune=sandybridge`).
    - <PERSON><PERSON><PERSON>: I don't think this is appropriate, as this is a variable target. 
        - Bai: Such that packages couldn't be reproducibly built?
            - Icenowy: Not quite, I don't quite think it's wrong per-se, but this value is...
            - Liushuyu: That it is not a value controlled by us?
    - Bai: So, `-mtune=skylake` instead?
        - Icenowy: That's fine by me.
        - Saki: Would that affect compatibility?
            - Icenowy: No, this is `-mtune`, and `-march` breaks compatibility. Though note that `-mtune` is not as contingent as it were with Pentium 4.
    - Saki: We should optimise for multiple targets...
        - Bai: That's an overlay target, but well, we have long since scrapped this idea.
        - Icenowy: Forget about it.
        - Liushuyu: There is also CPU runtime detection in some software.
            - Icenowy: Usually those without such detection won't be reliant on instruction sets that much anyways.
- Bai: I have another feature to talk about, though for being so complex, we probably won't do it with Core 9 - HWCAPS.
    - Icenowy: I think this is probably not a very valuable endeavour, though more manageable than our overlay mechanism.
    - Liushuyu: There's also the question of what package to support HWCAPS.
        - Bai: True, system will also bloat by multiple degrees if we go for this.
    - Liushuyu: We are also not sure that when compilers successfully implemented tree-vectorisation, we can't be sure if the code behaves as they should (also note buggy LLVM 13 code generation).
    - Leo: Can we include packages proven to have performance benefits in overlays?
        - Liushuyu: But we would have to prove this first.
    - Bai: As an alternative, a "-march=native" daemon to rebuild key performance components - like Windows' "ngen.exe." Just a concept though, let's not over think this for now.
        - Leo: Also very difficult ot make seamless.
- Icenowy: I will also introduce Ada support into GCC with Core 9. I will also need to bootstrap GCC for other architectures with a cross compiler.
    - Bai: And how's the progress at present?
        - Icenowy: I am currently trying to adapt cross compilers with Ciel.
    - Bai: How might you test Ada support? Coreboot?
        - Icenowy: With GCC (chuckles), as Coreboot would also need to compile its own Ada compiler at build-time (and Ada frontend is written in Ada).
        - Liushuyu: Thankfully the Rust frontend is written in C++.
- Bai: Any other notes?
    - Icenowy: I will need to look into offload support in GCC some more.
        - Liushuyu: Worry not, Fedora probably have a good reference for us.
        - Icenowy: It's just strange that when trying to build an offload-enabled GCC, it failed on my machine - somewhere around `liboffload`.
    - Bai: Any ahead-of-time warnings for GCC 11?
        - Liushuyu: I can talk briefly, but there are a lot of details. First there are split and removed headers, and you might want to fix includes in source files. Second, more severely, GCC might crash when building some softwares, as usual.
    - Bai: There might also be ABI breakage with Glibc 2.33, as noted in a [Reddit thread about Pacman](https://www.reddit.com/r/archlinux/comments/10rw94/yet_another_help_glibc_broke_pacman_request/).
        - Icenowy: This is so strange, they shouldn't have removed anything like this...
            - Liushuyu: Let's wait and see. Just test `bsdtar`.
- Bai: I will also run a survey on packages with static libraries, and disable LTO for all of them.
    - Liushuyu: There are two ways to go around this, one is to disable LTO, and another to use `-ffat-lto-objects` for these packages.
        - Bai: The second way seem to be viable, since some package only casually enabled static libraries.
            - Liushuyu: Fedora has fat LTO enabled system-wide. But this comes at a cost of size.
            - Icenowy: Yeah, this is not a big price to pay - we are AOSC OS at the end of the day.
                - Bai: Sure, let's go with this.
            - Liushuyu: Also note that Clang has a different flag as to GCC's, `-emit-llvm` to include LLVM bytecode.

PineBook Pro Issue Reporting with Icenowy Zheng
-----------------------------------------------
            
- Bai: Let's now move on to report PineBook Pro issues?
- Saki: Mesa 21.1.4 seem to be more stable with KDE.
    - Icenowy: Placebo? There doesn't seem to be many relevant changes with Panfrost, etc. Better test more carefully.
        - Saki: It does seem to crash less frequently now...
        - Icenowy: Also try open and close windows rapidly.
        - Liushuyu: Another test is to use your input method, sometimes the window might freeze up with buggy drivers.
- TheSaltedFish: When I use an input method, the candidate window seem to become overly large, and occludes windows with wallpaper content.
- Cyanoxygen: Garbled screen when hot-rebooting.
    - Icenowy: I know of a patch from Manjaro, but mine doesn't have this issue. This could be batch-related, and will require testers with newer PineBook Pro's.
        - Bai: There was a new revision with PineBook Pro's?
            - Icenowy: No idea, honestly.
- TheSaltedFish: The GPU frequency, I'm not sure... I was running TDE, the GPU frequency was limited to ~100MHz, and I changed it to 800MHz manually. This was a bit annoying.
    - Icenowy: Let me look into this. Are you using the rk64 kernel?
        - TheSaltedFish: Yes.
    - Icenowy: Also the mainline kernel is usable with 5.13, but it's slower at boot - the kernel is larger with our configuration, so there's that.

OSPP 2021 Student Progress Report
---------------------------------
    
- Bai: Yanqi... Let's talk about your work this week.
    - Yanqi: I have been tweaking parameters with LFS chapters 5 and 6, also a lot of replacing config.guess from upstream.
        - Icenowy: Which one are you talking about?
            - Yanqi: GMP. This package is needed by Binutil's GDB components, and it doesn't yet support LoongArch assembly.
                - Icenowy: I can take a look at that... Try `--disable-assembly`.
    - Yanqi: There's only one package in chapter 6 to address - GCC Pass 2.
    - Bai: Any plans for next week?
        - Yanqi: Basic software compilation, and introduce dpkg.
            - Bai: One note for dpkg, there are some optional features, but not much. APT though has more, just keep in mind that you can disable some features to make bootstrapping more convenient.
            - Icenowy: Also at this stage you really don't need APT.
    - Yanqi: Just one thought that we could use some Loongnix binaries (like dpkg) if necessary.
        - Bai: You probably won't run into this anyways.
- WordlessEcho: This week, in four days, I translated KPMCore and Kube, ~450 strings. Strings about SMART in KPMCore are skipped, I'm not confident with this. I also reviewed all strings in the projects I worked on. There are two people I find to be punctual in answering question - Tyson and Yunqiang.
    - WordlessEcho: The two projects I selected for this week are not particularly ideal... I had much difficulty and wasted a lot of time on things like SMART status strings, etc.
    - Bai: What do you think for next week? Something that you might like better?
        - WordlessEcho: Kube seems fine, but they seem to be in a bad maintenance state. I also had a hard time creating an account in Kube.
        - Saki: And what else do you want to do?
            - WordlessEcho: As I said, I will go with the plans we made a week before. I will think about what to do next when I get to them.
    - Bai: You are doing good so far, we are on point with your pace so far.
        - WordlessEcho: It could be better, especially since I wasted a lot of time on SMART.
            - Liushuyu: Also keep in mind that some strings are shorter while some are longer.
- WordlessEcho: I couldn't seem to find the SSH fingerprint for their GitLab, it mismatches with what I had when I pushed changes.
    - Bai: Share your WeChat ID with me so you could talk directly with them.
