+++
title = "小型脚本维护指南（征求意见稿）"
description = ""
date = 2020-07-17T02:41:06.700Z
[taxonomies]
tags = ["dev-sys"]
+++

# 写在前面

我们的 [scriptlets](https://github.com/AOSC-Dev/scriptlets) 仓库存放着大量的小型脚本。然而最开始的时候，仓库中不少脚本缺少文档和许可声明，使得后续的维护变得是否困难。因此，我们建立了这个文档，旨在规范小型脚本的引入和维护流程。

# 何为小型脚本

我们认为，每一个小型脚本都应该恰好提供一个非常明确的功能，它们最终都是为大型项目服务的。例如我们的 `pushpkg` 脚本就常与打包工具（如 Autobuild3、ACBS 和 Ciel）和软件仓库基础设施（如 LDAP 账户验证、文件上传等等）配合使用。

通常情况下，为每一个小型脚本分别建立仓库是没有必要的，我们就会将这些小型脚本收录到我们的 [scriptlets](https://github.com/AOSC-Dev/scriptlets) 仓库。

# 引入小型脚本

既然是小型脚本，将它们添加到我们的仓库中并不是什么难事。但是，为了保持 [scriptlets](https://github.com/AOSC-Dev/scriptlets) 仓库的相对有序性，引入流程应该遵循下面的要求：


每一个脚本（或每一组有着紧密关系的脚本组）...

- 必须放置在独立的目录（或子模块）中。
- 必须有独立且合适的许可证文件，而不能直接继承父项目的许可证。
- 必须要有一个或多个自述文档来描述其功能和用法。
- 如果引用自其它的项目，必须标明其来源。

# 维护小型脚本

维护者可以自行决定如何维护自己的小型脚本，但是维护者必须在脚本的自述文档或贡献指南中给出具体的维护流程，这样做是为了保证脚本的持续维护。