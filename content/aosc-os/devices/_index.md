+++
title = "AOSC OS Device Portal"
[extra]
list_section = false
+++

[livekit]: @/aosc-os/installation/livekit.md
[rpi-home]: @/aosc-os/devices/raspberrypi/_index.md
[applemac-home]: @/aosc-os/devices/apple/_index.md
[raspberry-pi-4b]: @/aosc-os/devices/raspberrypi/4b/_index.md
[apple-macmini9-1]: @/aosc-os/devices/apple/macmini9-1/_index.zh.md
[downloadpage]: https://aosc.io/downloads/alternative/

Here you can find some information about a specific device, including installation instructions, FAQs and troubleshootiong guides.

ARM64 Devices
------

For ARM64 devices which has UEFI firmware (such devices usually complies with SBSA standard), you can use ARM64 LiveKit to boot your device and install AOSC OS on it. For instructions using LiveKit, please checkout [LiveKit instructions][livekit].

### Table of Devices

Here is a list of devices supported by our community members. You can either checkout the vendor page or a specific hardware page.

| Device Name | Vendor | Platform | Device Type | Maintenance Status | Image Download |
| ------ | --------- | ------- | ------ | -------- | -------- |
| [Raspberry Pi 4B][raspberry-pi-4b] | [Raspberry Pi Foundation][rpi-home] | Broadcom BCM2711 | Single Board Computer） | Maintained by community members | Not available |
| [Mac Mini Late 2020 (Macmini9,1)][apple-macmini9-1] | [Apple][applemac-home] | Apple M1 | Mini Desktop | Maintained by community members | Not available |
| Pinebook Pro | Pine64 | Rockchip RK3399 |Laptop | Maintained by community members | [Download Page][downloadpage] |
| Rock64 | Pine64 | Rockchip RK3399 | Single Board Computer） | Maintained by community members | [Download Page][downloadpage] |
