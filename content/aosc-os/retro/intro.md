+++
title = "AOSC OS/Retro: An Introduction to Users and Maintainers"
description = "Enjoying AOSC OS on Your Retro Devices"
date = 2020-05-04T03:37:46.485Z
[taxonomies]
tags = ["sys-retro"]
+++

Now that we have introduced our [rationale](@/aosc-os/retro/rationale.md) to maintaining a Retro branch for AOSC OS (we will now refer to this as AOSC OS/Retro), this page will serve as an introduction to our design specifications and goals in maintaining this branch.

Specifications
=====

In terms of end-user experience and management characteristics, AOSC OS/Retro is a standard AOSC OS distribution. However, changes are made in terms of dependencies, features, and maintenance schedules. In brief, AOSC OS/Retro will...

- Come with limited architectural support - only those specified as <PERSON>tro will be maintained in accordance to the Retro specifications.
- *Share* the same [package tree](https://github.com/AOSC-Dev/aosc-os-abbs/) as the mainline distribution, as well as the [Core](https://github.com/AOSC-Dev/aosc-os-abbs/blob/stable/README.CORE.md).
- *Share* the same set of maintenance tools as the mainline distribution.
- Strip down package features in interest of *conserving storage and memory.*
- Ship in *different* flavours as otherwise would with the mainline distributions.
- Update on a slower schedule, *with a few exceptions.*

Now, we will present the commonalities and differences in detail.

Target Hardware
---------

AOSC OS/Retro currently support the following processing architectures and devices...

| Architecture                                        | Typical Devices                                                                      |
|-----------------------------------------------------|--------------------------------------------------------------------------------------|
| ARMv4, soft float with no Thumb support             | HP Jornada 710/720/728/820, etc.                                                     |
| ARMv6, hard float                                   | Raspberry Pi 1, etc.                                                                 |
| ARMv7, hard float with NEON SIMD support            | Raspberry Pi 2, etc.                                                                 |
| Intel 80486 compatible or above (`i486`)            | IBM Palm Top PC110, Toshiba Satellite 430CDT, Dell Latitude CPi, Asus EeePC 901 etc. |
| Loongson 2F (`loongson2f`)                          | Lemote YeeLoong 8089B/D, etc.                                                        |
| PowerPC G3 and above, 32-bit big endian (`powerpc`) | Apple iBook G3, Apple iMac G4, etc.                                                  |
| PowerPC G5 and above, 64-bit big endain (`ppc64`)   | Apple PowerMac G5, IBM eServer pSeries 610, etc.                                     |

Common Components and Tools
----------

As AOSC OS/Retro is *not* a fork but a *branch* of the mainline AOSC OS distribution, it will share not only the package tree, but also the Core and the maintenance tools as the mainline...

- Package Tree: [aosc-os-abbs](https://github.com/AOSC-Dev/aosc-os-abbs), on the [`retro`](https://github.com/AOSC-Dev/aosc-os-abbs/tree/retro) branch.
    - This means that AOSC OS/Retro will also use systemd as the default init system. *Now before you start yelling, systemd runs fine on an IBM Palm Top PC110 with a 33MHz 80486SL processor, 20MiB of RAM, and a 2GiB CF card. We've tested it to be sure.*
- AOSC OS Core will be shared, but only updates based on what are synced to the `retro` branch. See [Maintenance Schedule](#maintenance-schedules).
- Packaging/Maintenance Tools...
    - [Autobuild3](https://github.com/AOSC-Dev/autobuild3), for automatic packaging from Autobuild3 manifests.
    - [ACBS](https://github.com/AOSC-Dev/acbs), for tree-based Autobuild3 manifest management and packaging.
    - [Ciel](https://github.com/AOSC-Dev/ciel-rs), for containerised packaging.
    - Various [scriptlets](https://github.com/AOSC-Dev/scriptlets), shared with AOSC OS.

Dependencies
-----------

As AOSC OS/Retro targets storage and performance constraint devices, unlike its mainline counterpart, AOSC OS/Retro will ship packages with minimal optional feature enabled. Listed below are a few general rules to be followed...

- Base distribution (containing `admin-base`, `boot-base`, `core-base`, `editor-base`, `kernel-base`, `network-base`, `systemd-base`, `util-base`, and `web-base`) *must not* introduce dependency to Python (`python-2`, `python-3`).
- Language bindings (Java, Perl, Python, etc.) *must not* be enabled by default, unless required by another package essential to the distribution.
- Glibc is *only* to ship with the `C` and `C.UTF-8` locales pre-generated, with others generated by user configuration (defined in `/etc/locale.gen` but commented by default).
- Strip down *all optional* dependencies, unless such package is from the [Core](https://github.com/AOSC-Dev/aosc-os-abbs/blob/stable/groups/build-core), or otherwise discussed on a case-by-case basis.
- *All packages* are to be built with Link-Time Optimisation enabled, unless such optimisation leads to build failure (to be reported to upstream).
- Non-performance critical applications are to be built with the `-Os` (`AB_FLAGS_OS=1` in `autobuild/defines`) optimisation level to conserve space.
- Manpages and Texinfo documents will be shipped, but all other forms of documentation (examples, HTML, gtk-doc, etc.) will be *omitted*.
- Linux Kernel must boot *without* Dracut, unless used on a RAID setup. Dracut is *not* shipped with default distributions.

Distribution Features
-------------

AOSC OS/Retro will ship in several flavours, Base, X11, Server, and Trinity. All flavours will come fully localised (once locale is enabled by the end-user) as well as a generic, non-optimised Linux Kernel for each architecture.

- The Base flavour contains a minimal bootable and non-graphical system with tools essential to system management, basic text editing (GNU Nano), networking (wired and wireless), and basic user functions (compression, documentation browser, pager, power conservation, system monitoring, etc.).
- The X11 flavour contains a minimal bootable and graphical system, with all components listed above for the Base flavour, added with an X11-based desktop environment and other graphical utilities.
- The Server flavour contains a minimal bootable and server-oriented system, with all components listed above for the Base flavour, added with tools and utilities for server-oriented applications.
- The Trinity flavour contains a fully-featured bootable and graphical system, with all components listed above for the Base flavour, added with a fully-featured desktop interface powered by the [Trinity Desktop Environment](https://trinitydesktop.org/).
- Network management on all flavours will be provided with NetworkManager, and graphical systems will come with NetSurf as the default Web browser.

Extra packages, such as Firefox and more feature-complete desktop environments will be available from the [community repository](https://packages.aosc.io/), however, hardware requirement checks will be enforced based on processor and memory installed on your AOSC OS/Retro device (i.e., package installation will be aborted when attempting to install Firefox on a computer without SSE2 SIMD support).

Maintenance Schedules
-----------

AOSC OS/Retro will be maintained on the [`retro`](https://github.com/AOSC-Dev/aosc-os-abbs/tree/retro/) branch, sharing the same [package tree](https://github.com/AOSC-Dev/aosc-os-abbs/) with the mainline distribution. However, in interest of both the maintainer's reasonable maintenance effort, as well as the longevity and usability of the target devices, AOSC OS/Retro will update on an *annual schedule*.

After the first update cycle of a year, the `retro` branch will perform a dual-direction merge with the `stable` branch from the mainline distribution (`stable` => `retro`, then `retro` => `stable`). After which, *no further merge or reverse merge* will be allowed. Package versions in the `retro` branch will remain constant unless...

- Any bugfixes are found to be necessary by users or developers.
- A security update is made available that *requires* a version update. If necessary, changes could be [cherry-picked](https://git-scm.com/docs/git-cherry-pick) from the `stable` branch.

At the end of each annual cycle, a new distribution tarball will be made available on the [downloads page](https://aosc.io/downloads/), as well as an update CD image containing a local repository containing all system updates. A full AOSC OS/Retro repository will also be provided in forms of a tarball or a set of CD/DVD image.

Goals
=====

AOSC OS/Retro will be maintained with a few goals in mind, relating to system performance, storage requirements, and peripheral support. This chapter will also serve to outline AOSC OS/Retro's system requirements.

This chapter will then be split into sections, containing requirements and metrics shared and specific to each of our target architectures.

Common Metrics
-------------

- AOSC OS/Retro's Base flavour should install onto a 810MiB hard disk drive (commonly found with 486-class systems), while the X11 flavour should install onto a 1.2GB drive (commonly found on Intel x86 computers from ~1996).
    - After the system is installed, there should be enough space for a 64MiB swap area and future system updates (assuming one package is cached onto the hard disk at a time, using the update CD).
    - Users should expect to conserve ~100MiB of hard disk space for network- or internet-based system updates.
- AOSC OS/Retro should not require any form of network access for normal usage, assuming the user has obtained a copy of local repository.
- AOSC OS/Retro should support common ISA/EISA (or PCMCIA), PCI (or CardBus), PCI Express (or ExpressCard), SCSI, as well as USB, PS/2, Serial and Parallel peripherals.
- AOSC OS/Retro should support dial-up, 10/100/1000Mbps Ethernet, as well as 802.11a/b/g/n/ac/ax wireless connections.
- AOSC OS/Retro should boot from IDE/EIDE/CE-ATA/SATA/SCSI-based hard disk drives. AOSC OS/Retro may boot from USB, optical media, or other forms of external/removable storage, but this will not officially supported.

System Requirements
==================

General Advise
--------------

In order to ensure an acceptable and productive AOSC OS/Retro experience, we recommend the following when considering installation of AOSC OS/Retro on your devices:

- Minimum requirements are here for technicality's sake, it does not necessarily mean that AOSC OS/Retro would run with sufficient performance on said configuration. We have included specific advice for each architecture.
- Flash-based storage devices, such as bridged CompactFlash cards (connected via IDE, SCSI, etc.) will significantly improve system performance on devices from the 1990s. CompactFlash cards and adapters are affordable, consumes less power, and lowers overall system heat output when compared with the older, (now) frequently failing mechanical IDE hard disk drives.
- Control your expectations. While AOSC OS/Retro runs well on computers from well over 20 years ago, they won't run Crysis or even play YouTube videos. We advise that you plan your applications wisely, especially on devices that are severely performance limited.

x86/i486 Systems
----------------

On the 32-bit x86 architecture, AOSC OS/Retro Base and Server requires the following system components...

- Processor: Intel 80486 or compatible, FPU (Floating Point Unit) not required.
- System Bus: ISA, EISA, PCI, or PCI Express based system devices. MCA (Micro Channel Architecture) not supported.
- RAM: 12MiB (32MiB swap).
- Storage: 810MB (~514MiB).
    - (Ultra) DMA via PCI Bus Mastering will significantly improve system performance.
- Input Device: PS/2 or Serial Port Keyboard. Mouse not required.
- Display: VGA or compatible, or serial terminal.

AOSC OS/Retro X11 requires the following system components...

- Processor: Intel 80486 or compatible, FPU (Floating Point Unit) not required.
    - Intel Pentium II 233MHz, AMD K6, Cyrix MediaGX, Via C7 or above will significantly improve graphical experience.
    - Intel Pentium III 500MHz, AMD K6-II/III or above recommended for video playback using MPlayer.
- System Bus: ISA, EISA, PCI, or PCI Express based system devices. MCA (Micro Channel Architecture) not supported.
- RAM: 48MiB (64MiB swap).
    - 128MiB or above recommended for Internet browsing.
- Storage: 1.2GB (~1141MiB).
    - 4.0GB (~3814MiB) recommended for local multimedia storage.
    - (Ultra) DMA via PCI Bus Mastering will significantly improve system performance.
- Input Device: PS/2 or Serial Port Keyboard and Mouse.
    - Touchscreen will be supported via I2C, Serial, or USB connections.
- Display: VGA or compatible.
    - ISA/EISA video cards are *not recommended*, VESA Local Bus will significantly improve video performance.
    - PCI and PCI Express video cards recommended, especially those with OpenGL 2.1 support (often found after ~2002), as this will allow for GPU-based video playback acceleration.
    
AOSC OS/Retro Trinity requires the following system components...

- Processor: Intel Pentium II 233MHz, AMD K6, Cyrix MediaGX, Via C7 or above.
    - Intel Pentium III 500MHz, AMD K6-II/III or above recommended.
- System Bus: ISA, EISA, PCI, or PCI Express based system devices. MCA (Micro Channel Architecture) not supported.
- RAM: 256MiB (256MiB swap).
    - 512MiB or above recommended for Internet browsing.
- Storage: 3.2GB (~3052MiB).
    - 8GB (~7629MiB) recommended for local multimedia storage.
    - (Ultra) DMA via PCI Bus Mastering will significantly improve system performance.
- Input Device: PS/2 or Serial Port Keyboard and Mouse.
    - Touchscreen will be supported via I2C, Serial, or USB connections.
- Display: VGA or compatible.
    - PCI and PCI Express video cards recommended, especially those with OpenGL 2.1 support (often found after ~2002), as this will allow for GPU-based video playback acceleration.

PowerPC Systems
---------------

All AOSC OS/Retro flavours should run on all supported devices on this architecture - that is, PowerPC-based Apple Macintosh computers with New World ROM support. If you would like to run Trinity Desktop Environment on your PowerPC devices, you are advised to have at least 256MiB of RAM installed.

- Portables...
   - PowerBook G3 "Lombard" and "Pismo" models.
   - All iBook G3, iBook G4, PowerBook G4 models.
- Desktops...
   - Power Macintosh G3 "Blue and White" models.
   - All Power Macintosh G4 and G5 models.
   - All iMac G3 and G4 models.
   - All eMac models.
   - All G4-based Mac Mini models.
   - All G4- and G5-based Xserve models.

For storage requirements, you may refer to the x86 section above.

Loongson 2F Systems
-------------------

All AOSC OS/Retro flavours should run reasonably well on Loongson 2F-based devices (with performance close to an Intel Pentium III Coppermine). Currently, we only support the Lemote YeeLoong 8089 family of laptops.

For storage requirements, you may refer to the x86 section above.

ARMv4 Systems
-------------

For ARMv4, AOSC OS/Retro supports a limited range of devices. Specifically the HP Jornada 710/720/728/820 Windows CE handheld computers. AOSC OS/Retro Base and Server should run on any of the supported devices with their standard RAM capacity (32MiB or 64MiB), X11 requires that you install a 64MiB RAM capacity ROM card to ensure acceptable performance. Trinity Desktop Environment is not supported, for obvious performance reasons.

For storage requirements, you may refer to the x86 section above.

ARMv6 Systems
-------------

For ARMv6, AOSC OS/Retro currently support only the Raspberry Pi 1 models with 256MiB - 512MiB of RAM. All AOSC OS/Retro flavours should run reasonably well on these devices.

For storage requirements, you may refer to the x86 section above.

ARMv7 Systems
-------------

For ARMv7, AOSC OS/Retro currently support only the Raspberry Pi 2 models, with Allwinner device support planned. All AOSC OS/Retro flavours should run reasonably well on these devices.

For storage requirements, you may refer to the x86 section above.
