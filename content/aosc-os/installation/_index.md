+++
title = "Installing AOSC OS"
+++

This page lists installation guides for AOSC OS and AOSC OS/Retro, as well
as system requirements to help you decide whether AOSC OS or AOSC OS/Retro
is suitable for your device.

LiveKit Installation
====================

For the majority of x86-, PowerPC-, OpenPOWER- and Loongson-based desktop
devices, as well as some AArch64-based devices, AOSC OS and AOSC OS/Retro
could be easily installed with LiveKit. For details on how to make use of
LiveKit, please refer to the following page for details.

- [Using LiveKit](@/aosc-os/installation/livekit.md)

Windows Subsystem for Linux
===========================

For details on how to install AOSC OS on WSL, please refer to the following
page for details.

- [Installation/WSL](@/aosc-os/installation/wsl.md)

Raw System Images
=================

For ARM and RISC-V devices which do not make use of (U)EFI boot facilities,
you would want to use device-specific raw system images. The downloads for
which are listed in our
[Alternative Downloads](https://aosc.io/downloads/alternative/) page.

For instructions on how to write these images, please refer to the
following page for details.

- [Writing System Images](@/aosc-os/installation/rawimgs.md)

Manual Installation
===================

For uncommon, advanced, and LiveKit-incompatible installation procedures,
please refer to our library of
[manual installation guides](/aosc-os/installation/manual/) for details.

System Requirements
===================

For a list of system requirements for AOSC OS and AOSC OS/Retro, please
refer to our library of [system requirements](/aosc-os/installation/sysreq/)
for details.
