+++
title = "List of Announced AOSAs (2020)"
description = "Archive of Announced AOSC OS Security Advisories (2020)"
date = 2021-04-04T03:34:57.724Z
[taxonomies]
tags = ["aosa"]
[extra]
page_hack = "big-min-table-cell-width"
+++

# Security updates

| AOSA | Package | Vendor Advisory |
| --- | --- | --- |
| AOSA-2020-0001 | Update virtualbox to 6.1.2 | CVE-2020-2674, CVE-2020-2678, CVE-2020-2681, CVE-2020-2682, CVE-2020-2689, CVE-2020-2690, CVE-2020-2691, CVE-2020-2692, CVE-2020-2693, CVE-2020-2698, CVE-2020-2701, CVE-2020-2702, CVE-2020-2703, CVE-2020-2704, CVE-2020-2705, CVE-2020-2725, CVE-2020-2726, CVE-2020-2727 |
| AOSA-2020-0002 | Update thunderbird to 68.5.0 | CVE-2020-6792, CVE-2020-6793, CVE-2020-6794, CVE-2020-6795, CVE-2020-6798, CVE-2020-6800 |
| AOSA-2020-0003 | Update chromium, google-chrome to 80.0.3987.149 | CVE-2019-20503, CVE-2020-6422, CVE-2020-6424, CVE-2020-6425, CVE-2020-6426, CVE-2020-6427, CVE-2020-6428, CVE-2020-6429, CVE-2020-6449 |
| AOSA-2020-0004 | Update opera to 67.0.3575.97 | CVE-2019-20503, CVE-2020-6422, CVE-2020-6424, CVE-2020-6425, CVE-2020-6426, CVE-2020-6427, CVE-2020-6428, CVE-2020-6429, CVE-2020-6449 |
| AOSA-2020-0005 | Update vivaldi to 2.11.1811.49 | CVE-2019-20503, CVE-2020-6422, CVE-2020-6424, CVE-2020-6425, CVE-2020-6426, CVE-2020-6427, CVE-2020-6428, CVE-2020-6429, CVE-2020-6449 |
| AOSA-2020-0006 | Update libvncserver to 0.9.12-1 | CVE-2018-7225, CVE-2018-7226 |
| AOSA-2020-0007 | Update openjdk-8 to 8u252 | CVE-2020-2754, CVE-2020-2755, CVE-2020-2756, CVE-2020-2757, CVE-2020-2773, CVE-2020-2781, CVE-2020-2800, CVE-2020-2803, CVE-2020-2805, CVE-2020-2830 |
| AOSA-2020-0008 | Update openjdk to 11.0.7 | CVE-2020-2803, CVE-2020-2805, CVE-2020-2816, CVE-2020-2781, CVE-2020-2830, CVE-2020-2767, CVE-2020-2800, CVE-2020-2778, CVE-2020-2754, CVE-2020-2755, CVE-2020-2773, CVE-2020-2756, CVE-2020-2757 |
| AOSA-2020-0009 | Update ntp to 4.2.8p13 | CVE-2019-8936 |
| AOSA-2020-0010 | Update libssh2 to 1.8.2 | CVE-2019-3855, CVE-2019-3856, CVE-2019-3857, CVE-2019-3858, CVE-2019-3859, CVE-2019-3860, CVE-2019-3861, CVE-2019-3862, CVE-2019-3863 |
| AOSA-2020-0011 | Update libjpeg-turbo to 2.0.2 | CVE-2018-20330, CVE-2018-19664, CVE-2018-14498, CVE-2018-11813, CVE-2018-1152 |
| AOSA-2020-0012 | Update evolution to 3.30.5 | CVE-2018-15587 |
| AOSA-2020-0013 | Update dovecot to 2.3.7.2 | CVE-2019-11494, CVE-2019-11499, CVE-2019-11500 |
| AOSA-2020-0014 | Update freeradius to 3.0.19 | CVE-2019-11234, CVE-2019-11235 |
| AOSA-2020-0015 | Update libvirt to 5.10.0 | CVE-2019-10132, CVE-2019-10161, CVE-2019-10166, CVE-2019-10167, CVE-2019-10168 |
| AOSA-2020-0016 | Update openexr to 2.3.0-3 | CVE-2018-18444 |
| AOSA-2020-0017 | Update evince to 3.36.0-2 | CVE-2019-11459 |
| AOSA-2020-0018 | Update neovim, vim to 0.3.6, ^8.1.1365 | CVE-2019-12735 |
| AOSA-2020-0019 | Update imagemagick to 6.9.10-50 | CVE-2019-11598 |
| AOSA-2020-0020 | Update irssi to 1.2.1 | CVE-2019-13045, IRSSI-SA-2019-06 |
| AOSA-2020-0021 | Update pam-u2f to 1.0.8 | CVE-2019-12209, CVE-2019-12210 |
| AOSA-2020-0022 | Update libsndfile to 1.0.28-4 | CVE-2017-16942, CVE-2018-19432, CVE-2018-19661, CVE-2018-19662, CVE-2018-19758, CVE-2019-3832 |
| AOSA-2020-0023 | Update zeromq to 4.3.2 | CVE-2019-6250, CVE-2019-13132 |
| AOSA-2020-0024 | Update docker to 18.09.7 | CVE-2018-15664 |
| AOSA-2020-0025 | Update gnupg to 2.2.17 | https://lists.gnupg.org/pipermail/gnupg-announce/2019q3/000439.html |
| AOSA-2020-0026 | Update glib to 2.64.1 | CVE-2019-12450, CVE-2019-13012 |
| AOSA-2020-0027 | Update sqlalchemy to 1.2.18-1 | CVE-2019-7164, CVE-2019-7548 |
| AOSA-2020-0028 | Update dotnet-runtime to 2.2.6, dotnet-sdk to 2.2.301 | CVE-2019-1075 |
| AOSA-2020-0029 | Update qemu to 4.0.0 | CVE-2018-20815 |
| AOSA-2020-0030 | Update dosbox to 0.74-3 | CVE-2019-7165, CVE-2019-12594 |
| AOSA-2020-0031 | Update bzip2 to 1.0.7 | CVE-2019-12900 |
| AOSA-2020-0032 | Update libmediainfo to 19.04 | CVE-2019-11372, CVE-2019-11373 |
| AOSA-2020-0033 | Update bind to 9.12.4-P2 | CVE-2019-6471 |
| AOSA-2020-0034 | Update x11-lib to 7.7.20200322 | CVE-2017-2626 |
| AOSA-2020-0035 | Update rdesktop to 1.8.6 | |
| AOSA-2020-0036 | Update redis to 5.0.5 | CVE-2019-10192, CVE-2019-10193 |
| AOSA-2020-0037 | Update squid to 4.8 | CVE-2019-12854, CVE-2019-12529, CVE-2019-12525, CVE-2019-12527, CVE-2019-13345 |
| AOSA-2020-0038 | Update atril to 1.24.0 | CVE-2019-1010006 |
| AOSA-2020-0039 | Update libmspack to 0.9.1alpha-1 | CVE-2019-1010305 |
| AOSA-2020-0040 | Update sdl2-image to 2.0.5 | CVE-2019-5051, CVE-2019-5052, CVE-2019-5057, CVE-2019-5058, CVE-2019-5059, CVE-2019-5060, TALOS-2019-0820, TALOS-2019-0821, TALOS-2019-0841, TALOS-2019-0842, TALOS-2019-0843, TALOS-2019-0844 |
| AOSA-2020-0041 | Update openjdk to 1.8.3 | CVE-2019-10181, CVE-2019-10182, CVE-2019-10185 |
| AOSA-2020-0042 | Update sigil to 0.9.16 | CVE-2019-14452 |
| AOSA-2020-0043 | Update ffmpeg to 4.1.4 | CVE-2019-12730 |
| AOSA-2020-0044 | Update faad2 to 2.8.8-1 | CVE-2019-15296 |
| AOSA-2020-0045 | Update djvulibre to 3.5.27.1 | CVE-2019-15142, CVE-2019-15143, CVE-2019-15144, CVE-2019-15145 |
| AOSA-2020-0046 | Update patch to 2.7.6-2 | CVE-2018-20969, CVE-2019-13636, CVE-2019-13638 |
| AOSA-2020-0047 | Update giflib to 5.1.8 | CVE-2019-15133 |
| AOSA-2020-0048 | Update subversion to 1.12.2 | CVE-2018-11782, CVE-2019-0203 |
| AOSA-2020-0049 | Update postgresql to 11.5 | CVE-2019-10208, CVE-2019-10209 |
| AOSA-2020-0050 | Update cuberite to 2019-06-11 | CVE-2019-15516 |
| AOSA-2020-0051 | Update exim to 4.92.2 | CVE-2019-15846 |
| AOSA-2020-0052 | Update hostapd to 2.9 | CVE-2019-13377 |
| AOSA-2020-0053 | Update wpa-supplicant to 2.9 | CVE-2019-13377 |
| AOSA-2020-0054 | Update libmirage to 3.2.4 | CVE-2019-15540 |
| AOSA-2020-0055 | Update irssi to 1.2.2 | CVE-2019-15717 |
| AOSA-2020-0056 | Update wireshark to 3.0.3 | CVE-2019-13619, wnpa-sec-2019-20 |
| AOSA-2020-0057 | Update php7 to 7.3.9 | CVE-2019-11041, CVE-2019-11042 |
| AOSA-2020-0058 | Update python-2, python-3 to 2.7.17-1, 3.8.2-3 | CVE-2019-16056 |
| AOSA-2020-0059 | Update expat to version 2.2.8 | CVE-2019-15903 |
| AOSA-2020-0060 | Update phpmyadmin to 5.0.2 | CVE-2019-11768, CVE-2019-12616 |
| AOSA-2020-0061 | Update commons-compress to 1.19 | CVE-2019-12402 |
| AOSA-2020-0062 | Update e2fsprogs to 1.45.4 | CVE-2019-5094, TALOS-2019-0887 |
| AOSA-2020-0063 | Update file to 5.38-1 | CVE-2019-18218 |
| AOSA-2020-0064 | Update binutils to 2.31.1 | CVE-2017-15938, CVE-2017-15939, CVE-2017-15996, CVE-2017-16826, CVE-2017-16827, CVE-2017-16828, CVE-2017-16829, CVE-2017-16830, CVE-2017-16831, CVE-2017-16832, CVE-2018-6323, CVE-2018-6543, CVE-2018-759, CVE-2018-6872, CVE-2018-7208, CVE-2018-7568, CVE-2018-7569, CVE-2018-7570, CVE-2018-7642, CVE-2018-7643, CVE-2018-8945, CVE-2018-10372, CVE-2018-10373, CVE-2018-10534, CVE-2018-10535 |
| AOSA-2020-0065 | Update virtualbox to 6.1.6 | CVE-2020-2902, CVE-2020-2959, CVE-2020-2905, CVE-2020-2908, CVE-2020-2758, CVE-2020-2894, CVE-2020-2929, CVE-2020-2911, CVE-2020-2907, CVE-2020-2958, CVE-2020-2913, CVE-2020-2914, CVE-2020-2910, CVE-2020-2951, CVE-2020-2741, CVE-2020-2748, CVE-2020-2909 |
| AOSA-2020-0066 | Update libreoffice, libreoffice-help to 6.2.7 | CVE-2019-9854 |
| AOSA-2020-0067 | Update google-chrome to 81.0.4044.113 | CVE-2020-6450, CVE-2020-6451, CVE-2020-6452, CVE-2020-6454, CVE-2020-6423, CVE-2020-6455, CVE-2020-6430, CVE-2020-6456, CVE-2020-6431, CVE-2020-6432, CVE-2020-6433, CVE-2020-6434, CVE-2020-6435, CVE-2020-6436, CVE-2020-6437, CVE-2020-6438, CVE-2020-6439, CVE-2020-6440, CVE-2020-6441, CVE-2020-6442, CVE-2020-6443, CVE-2020-6444, CVE-2020-6445, CVE-2020-6446, CVE-2020-6447, CVE-2020-6448, CVE-2020-6457 |
| AOSA-2020-0068 | Update libexif to 0.6.21-5 | CVE-2019-9278 |
| AOSA-2020-0069 | Update screen 4.7.0-1 | No CVE assigned |
| AOSA-2020-0070 | Update sudo to 1.8.31 | CVE-2019-18634 |
| AOSA-2020-0071 | Update pillow to 6.2.0 | CVE-2019-16865 |
| AOSA-2020-0072 | Update openssl to 1.0.2t/1.1.1.d | CVE-2019-1563 |
| AOSA-2020-0073 | Update sdl2 to 2.0.10 | CVE-2019-7572, CVE-2019-7573, CVE-2019-7574, CVE-2019-7575, CVE-2019-7576, CVE-2019-7577, CVE-2019-7578, CVE-2019-7635, CVE-2019-7636, CVE-2019-7638 |
| AOSA-2020-0074 | Update chromium to 81.0.4044.113 | CVE-2020-6450, CVE-2020-6451, CVE-2020-6452, CVE-2020-6454, CVE-2020-6423, CVE-2020-6455, CVE-2020-6430, CVE-2020-6456, CVE-2020-6431, CVE-2020-6432, CVE-2020-6433, CVE-2020-6434, CVE-2020-6435, CVE-2020-6436, CVE-2020-6437, CVE-2020-6438, CVE-2020-6439, CVE-2020-6440, CVE-2020-6441, CVE-2020-6442, CVE-2020-6443, CVE-2020-6444, CVE-2020-6445, CVE-2020-6446, CVE-2020-6447, CVE-2020-6448, CVE-2020-6457 |
| AOSA-2020-0075 | Update libssh to 0.8.9 | CVE-2020-1730 |
| AOSA-2020-0076 | Update tor to 0.4.2.7 | CVE-2020-10592 |
| AOSA-2020-0077 | Update openssl to 1.1.1e | CVE-2019-1551 |
| AOSA-2020-0078 | Update openssl to 1.1.1g | CVE-2020-1967 |
| AOSA-2020-0079 | Update elvis to v2.2_1-pre3 | CVE-2019-12735 |
| AOSA-2020-0080 | Update firefox to 75.0 | CVE-2020-6821, CVE-2020-6822, CVE-2020-6823, CVE-2020-6824, CVE-2020-6825, CVE-2020-6826, MFSA2020-12 |
| AOSA-2020-0081 | Update webkit2gtk 2.26.2 | CVE-2020-11793, WSA-2020-0004 |
| AOSA-2020-0082 | Update thunderbird to 68.7.0 | CVE-2020-6819, CVE-2020-6820, CVE-2020-6821, CVE-2020-6822, CVE-2020-6825, MFSA-2020-14 |
| AOSA-2020-0083 | Update re2c to 1.3-1 | No CVE assigned |
| AOSA-2020-0084 | Update git to 2.24.3 | CVE-2020-11008 |
| AOSA-2020-0085 | Update freerdp to 2.0.0 | CVE-2020-11521, CVE-2020-11522, CVE-2020-11523, CVE-2020-11524, CVE-2020-11525, CVE-2020-11526 |
| AOSA-2020-0086 | Update Trinity Desktop Environment to 14.0.7 | CVE-2018-19872, CVE-2019-14744 |
| AOSA-2020-0087 | Update ceph to 13.2.7 | CVE-2019-10222 |
| AOSA-2020-0088 | Update twisted to 19.7.0 | CVE-2019-12387, CVE-2019-12855 |
| AOSA-2020-0089 | Update webkit2gtk to 2.28.1 | CVE-2020-11793, WSA-2020-0004 |
| AOSA-2020-0090 | Update httpie to 1.0.3-1 | CVE-2019-10751, SNYK-PYTHON-HTTPIE-460107 |
| AOSA-2020-0091 | Update git to 2.26.2 | CVE-2020-11008 |
| AOSA-2020-0092 | Update bsdiff to 4.3-1 | CVE-2014-9862, https://www.freebsd.org/security/advisories/FreeBSD-SA-16:25.bspatch.asc |
| AOSA-2020-0093 | Update firejail to 0.9.62 | CVE-2019-12499, CVE-2019-12589 |
| AOSA-2020-0094 | Update opensmtpd to 6.6.4 | CVE-2020-8794 |
| AOSA-2020-0095 | Update spamassassin to 3.4.4 | CVE-2020-1930, CVE-2020-1931 |
