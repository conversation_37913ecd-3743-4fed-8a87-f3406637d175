+++
title = "List of Announced AOSAs (2018)"
description = "Archive of Announced AOSC OS Security Advisories (2018)"
date = 2020-05-04T03:34:53.667Z
[taxonomies]
tags = ["aosa"]
[extra]
page_hack = "big-min-table-cell-width"
+++

| AOSA | Suggestion(s) | Fixed CVE(s) |
| ------------ | --------------- | ------------------ |
| AOSA-2018-0001 | Revoked |
| AOSA-2018-0002 | Update ICU to 58.2-6 | [CVE-2017-15422](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15422) |
| AOSA-2018-0003 | Update Fossil to 2.4 | [CVE-2017-17459](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-17459) |
| AOSA-2018-0004 | Update Firefox to 57.0.4 |
| AOSA-2018-0005 | Update Brave Browser to 0.19.123dev | [CVE-2017-15429](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15429) |
| AOSA-2018-0006 | Update Opera to 50.0.2762.45 | [CVE-2017-15429](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15429) |
| AOSA-2018-0007 | Update Podofo to 0.9.5-1 | [CVE-2017-5852](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5852), [CVE-2017-5853](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5853), [CVE-2017-5854](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5854), [CVE-2017-5855](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5855), [CVE-2017-5886](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5886), [CVE-2017-6840](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-6840), [CVE-2017-6842](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-6842), [CVE-2017-6843](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-6843), [CVE-2017-6844](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-6844), [CVE-2017-6847](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-6847), [CVE-2017-6848](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-6848), [CVE-2017-7378](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-7378), [CVE-2017-7379](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-7379), [CVE-2017-7380](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-7380), [CVE-2017-7381](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-7381), [CVE-2017-7382](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-7382), [CVE-2017-7383](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-7383), [CVE-2017-7994](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-7994), [CVE-2017-8787](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-8787) |
| AOSA-2018-0008 | Update WildMIDI to 0.4.2 | [CVE-2017-11661](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-11661), [CVE-2017-11662](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-11662), [CVE-2017-11663](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-11663), [CVE-2017-11664](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-11664) |
| AOSA-2018-0009 | Update Systemd to 235-3 | [CVE-2017-15908](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15908) |
| AOSA-2018-0010 | Update LibEXIF to 0.6.21-4 | [CVE-2016-6328](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2016-6328), [CVE-2017-7544](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-7544) |
| AOSA-2018-0011 | Update Wayland to 1.13.0-1 | [CVE-2017-16612](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-16612) |
| AOSA-2018-0012 | Update Perl DBD::MySQL to 4.043-2 | [CVE-2017-10788](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-10788) |
| AOSA-2018-0013 | Update Raptor2 to 2.0.15-3 |
| AOSA-2018-0014 | Update GStreamer Ugly Plugins (0.10) to 0.10.19-6 | [CVE-2017-5846](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5846), [CVE-2017-5847](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5847) |
| AOSA-2018-0015 | Update GNU C Library to 2.26-5 | [CVE-2017-15804](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15804), [CVE-2017-15670](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15670), [CVE-2017-15671](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15671), [CVE-2017-1000408](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-1000408), [CVE-2017-1000409](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-1000409) |
| AOSA-2018-0016 | Update Ncurses to 6.0+20171230 | [CVE-2017-10684](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-10684), [CVE-2017-10685](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-10685), [CVE-2017-11112](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-11112), [CVE-2017-11113](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-11113), [CVE-2017-13728](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-13728), [CVE-2017-13729](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-13729), [CVE-2017-13730](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-13730), [CVE-2017-13731](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-13731), [CVE-2017-13732](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-13732), [CVE-2017-13733](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-13733), [CVE-2017-16879](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-16879) |
| AOSA-2018-0017 | Update Linux Kernel (4.14, "Main") to 4.14.12 | [CVE-2017-5753](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5753), [CVE-2017-5715](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5715), [CVE-2017-5754](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5754) |
| AOSA-2018-0018 | Update Linux Kernel (4.9, "LTS") to 4.9.75 | [CVE-2017-5753](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5753), [CVE-2017-5715](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5715), [CVE-2017-5754](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5754) |
| AOSA-2018-0019 | Update Irssi to 1.0.6 | [CVE-2018-5205](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5205), [CVE-2018-5206](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5206), [CVE-2018-5207](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5207), [CVE-2018-5208](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5208) |
| AOSA-2018-0020 | Update NVIDIA Proprietary Driver Package to 390.12 | [CVE-2017-5715](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5715), [CVE-2017-5753](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5753), [CVE-2017-5754](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5754) |
| AOSA-2018-0021 | Update Linux Kernel (4.14, "Main") to 4.14.12-1 |
| AOSA-2018-0022 | Update Linux Kernel (4.14, "Libre") to 4.14.12 | [CVE-2017-5715](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5715), [CVE-2017-5753](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5753), [CVE-2017-5754](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5754) |
| AOSA-2018-0023 | Update GCC (GNU Compiler Collection) to 7.2.0-2 |
| AOSA-2018-0024 | Update Intel Microcode/uCode to 20180108 |
| AOSA-2018-0025 | Update WebKit2GTK+ to 2.18.5 | [CVE-2017-5715](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5715), [CVE-2017-5753](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5753) |
| AOSA-2018-0026 | Update Flash Player PepperAPI Plugin to 28.0.0.137 | [CVE-2018-4871](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4871) |
| AOSA-2018-0027 | Update Poppler to 0.60.1-1 | [CVE-2017-1000456](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-1000456) |
| AOSA-2018-0028 | Update IcoUtils (.ICO Utilities) to 0.32.2 | [CVE-2017-5208](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5208), [CVE-2017-5332](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5332), [CVE-2017-5333](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5333), [CVE-2017-6009](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-6009), [CVE-2017-6010](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-6010), [CVE-2017-6011](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-6011) |
| AOSA-2018-0029 | Update Brave Browser to 0.19.131dev |
| AOSA-2018-0030 | Update Transmission to 2.92-2 | [CVE-2018-5702](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5702) |
| AOSA-2018-0031 | Update Wireshark to 2.4.4 | [CVE-2018-5334](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5334), [CVE-2018-5335](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5335), [CVE-2018-5336](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5336), [CVE-2017-5753](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5753) |
| AOSA-2018-0032 | Update LibVorbis to 1.3.5-2 | [CVE-2017-14632](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-14632), [CVE-2017-14633](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-14633) |
| AOSA-2018-0033 | Update PHP to 7.2.1 |
| AOSA-2018-0034 | Update Bind to 9.11.2.P1 | [CVE-2017-3145](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-3145) |
| AOSA-2018-0035 | Update Unbound to 1.6.8 | [CVE-2017-15105](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15105) |
| AOSA-2018-0036 | Update Rsync to 3.1.2-4 | [CVE-2018-5764](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5764) |
| AOSA-2018-0037 | Update Squid to 3.5.27-1 | [CVE-2018-1000024](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1000024), [CVE-2018-1000027](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1000027) |
| AOSA-2018-0038 | Update OpenJDK to 8u162b12 | [CVE-2018-2581](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2581), [CVE-2018-2582](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2582), [CVE-2018-2588](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2588), [CVE-2018-2599](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2599), [CVE-2018-2602](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2602), [CVE-2018-2603](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2603), [CVE-2018-2618](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2618), [CVE-2018-2627](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2627), [CVE-2018-2629](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2629), [CVE-2018-2633](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2633), [CVE-2018-2634](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2634), [CVE-2018-2637](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2637), [CVE-2018-2638](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2638), [CVE-2018-2639](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2639), [CVE-2018-2641](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2641), [CVE-2018-2657](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2657), [CVE-2018-2663](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2663), [CVE-2018-2675](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2675), [CVE-2018-2677](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2677), [CVE-2018-2678](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2678), [CVE-2018-2679](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2679) |
| AOSA-2018-0039 | Update DHCP to 4.3.6-1 | [CVE-2017-3144](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-3144) |
| AOSA-2018-0040 | Update VirtualBox to 5.2.6 | [CVE-2017-3736](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-3736), [CVE-2017-3736](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-3736), [CVE-2017-5645](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5645), [CVE-2017-5715](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5715), [CVE-2018-2676](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2676), [CVE-2018-2685](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2685), [CVE-2018-2686](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2686), [CVE-2018-2687](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2687), [CVE-2018-2688](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2688), [CVE-2018-2689](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2689), [CVE-2018-2690](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2690), [CVE-2018-2693](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2693), [CVE-2018-2694](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2694), [CVE-2018-2698](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2698) |
| AOSA-2018-0041 | Update Firefox to 58.0 | [CVE-2018-5089](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5089), [CVE-2018-5090](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5090), [CVE-2018-5091](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5091), [CVE-2018-5092](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5092), [CVE-2018-5093](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5093), [CVE-2018-5094](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5094), [CVE-2018-5095](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5095), [CVE-2018-5097](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5097), [CVE-2018-5098](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5098), [CVE-2018-5099](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5099), [CVE-2018-5100](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5100), [CVE-2018-5101](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5101), [CVE-2018-5102](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5102), [CVE-2018-5103](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5103), [CVE-2018-5104](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5104), [CVE-2018-5105](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5105), [CVE-2018-5106](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5106), [CVE-2018-5107](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5107), [CVE-2018-5108](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5108), [CVE-2018-5109](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5109), [CVE-2018-5110](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5110), [CVE-2018-5111](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5111), [CVE-2018-5112](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5112), [CVE-2018-5113](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5113), [CVE-2018-5114](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5114), [CVE-2018-5115](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5115), [CVE-2018-5116](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5116), [CVE-2018-5117](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5117), [CVE-2018-5118](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5118), [CVE-2018-5119](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5119), [CVE-2018-5121](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5121), [CVE-2018-5122](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5122) |
| AOSA-2018-0042 | Update cURL to 7.58.0 | [CVE-2018-1000005](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1000005), [CVE-2018-1000007](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1000007) |
| AOSA-2018-0043 | Update Pale Moon to 27.6.2 | [CVE-2017-7832](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-7832), [CVE-2017-7833](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-7833), [CVE-2017-7835](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-7835), [CVE-2017-7840](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-7840) |
| AOSA-2018-0044 | Update WebKit2GTK+ to 2.18.6 | [CVE-2018-4088](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4088), [CVE-2018-4096](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4096), [CVE-2017-7153](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-7153), [CVE-2017-7160](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-7160), [CVE-2017-7161](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-7161), [CVE-2017-7165](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-7165), [CVE-2017-13884](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-13884), [CVE-2017-13885](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-13885) |
| AOSA-2018-0045 | Update Chromium and Google Chrome to 64.0.3282.119 | [CVE-2018-6031](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6031), [CVE-2018-6032](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6032), [CVE-2018-6033](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6033), [CVE-2018-6034](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6034), [CVE-2018-6035](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6035), [CVE-2018-6036](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6036), [CVE-2018-6037](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6037), [CVE-2018-6038](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6038), [CVE-2018-6039](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6039), [CVE-2018-6040](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6040), [CVE-2018-6041](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6041), [CVE-2018-6042](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6042), [CVE-2018-6043](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6043), [CVE-2018-6045](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6045), [CVE-2018-6046](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6046), [CVE-2018-6047](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6047), [CVE-2018-6048](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6048), [CVE-2017-6049](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-6049), [CVE-2018-6050](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6050), [CVE-2018-6051](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6051), [CVE-2018-6052](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6052), [CVE-2018-6053](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6053), [CVE-2018-6054](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6054), [CVE-2017-15420](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15420) |
| AOSA-2018-0046 | Update Pale Moon to 27.7.1 |
| AOSA-2018-0047 | Update w3m to 20180125 | [CVE-2018-6196](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6196), [CVE-2018-6197](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6197), [CVE-2018-6198](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6198) |
| AOSA-2018-0048 | Update Systemd to 235-4 | [CVE-2017-18078](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-18078) |
| AOSA-2018-0049 | Update ClamAV to 0.99.3 | [CVE-2017-12374](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-12374), [CVE-2017-12375](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-12375), [CVE-2017-12376](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-12376), [CVE-2017-12377](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-12377), [CVE-2017-12378](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-12378), [CVE-2017-12379](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-12379), [CVE-2017-12380](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-12380) |
| AOSA-2018-0050 | Update Libtasn1 to 4.13 | [CVE-2018-6003](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6003) |
| AOSA-2018-0051 | Update GraphicsMagick to 1.3.28 |
| AOSA-2018-0052 | Update Dovecot to 2.2.29.1-1 | [CVE-2017-15132](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15132) |
| AOSA-2018-0053 | Update GCab to 0.7-1 | [CVE-2018-5345](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5345) |
| AOSA-2018-0054 | Update Node.js to 9.2.1 | [CVE-2017-3738](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-3738), [CVE-2017-15896](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15896), [CVE-2017-15897](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15897) |
| AOSA-2018-0055 | Update Thunderbird to 52.6.0 | [CVE-2018-5089](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5089), [CVE-2018-5095](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5095), [CVE-2018-5096](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5096), [CVE-2018-5097](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5097), [CVE-2018-5098](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5098), [CVE-2018-5099](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5099), [CVE-2018-5102](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5102), [CVE-2018-5103](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5103), [CVE-2018-5104](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5104), [CVE-2018-5117](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5117) |
| AOSA-2018-0056 | Update Firefox to 58.0.1 | [CVE-2018-5124](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5124) |
| AOSA-2018-0057 | Update SoX to 14.4.2-2 | [CVE-2017-15370](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15370), [CVE-2017-15371](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15371) |
| AOSA-2018-0058 | Update Mozilla JavaScript Runtime 52 (js-52) to 52.6.0 | [CVE-2017-7828](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-7828), [CVE-2018-5098](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5098), [CVE-2018-5099](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5099) |
| AOSA-2018-0059 | Update NVIDIA Proprietary Driver, 340.x to 340.106 |
| AOSA-2018-0060 | Update Pale Moon to 27.7.2 | [CVE-2018-5102](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5102), [CVE-2018-5122](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5122) |
| AOSA-2018-0061 | Update p7zip (POSIX 7-Zip) to 16.02-1 | [CVE-2017-17969](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-17969) |
| AOSA-2018-0062 | Update Chromium and Google Chrome to 64.0.3282.140 |
| AOSA-2018-0063 | Update Brave Browser to 0.20.38dev | [CVE-2018-6031](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6031), [CVE-2018-6032](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6032), [CVE-2018-6033](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6033), [CVE-2018-6034](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6034), [CVE-2018-6035](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6035), [CVE-2018-6036](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6036), [CVE-2018-6037](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6037), [CVE-2018-6038](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6038), [CVE-2018-6039](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6039), [CVE-2018-6040](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6040), [CVE-2018-6041](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6041), [CVE-2018-6042](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6042), [CVE-2018-6043](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6043), [CVE-2018-6045](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6045), [CVE-2018-6046](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6046), [CVE-2018-6047](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6047), [CVE-2018-6048](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6048), [CVE-2017-6049](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-6049), [CVE-2018-6050](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6050), [CVE-2018-6051](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6051), [CVE-2018-6052](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6052), [CVE-2018-6053](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6053), [CVE-2018-6054](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6054), [CVE-2017-15420](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15420) |
| AOSA-2018-0064 | Update LibVPX to 1.5.0-3 | [CVE-2017-13194](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-13194) |
| AOSA-2018-0065 | Update Vivaldi to 1.14.1077.45 | [CVE-2018-6031](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6031), [CVE-2018-6032](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6032), [CVE-2018-6033](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6033), [CVE-2018-6034](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6034), [CVE-2018-6035](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6035), [CVE-2018-6036](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6036), [CVE-2018-6037](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6037), [CVE-2018-6038](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6038), [CVE-2018-6039](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6039), [CVE-2018-6040](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6040), [CVE-2018-6041](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6041), [CVE-2018-6042](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6042), [CVE-2018-6043](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6043), [CVE-2018-6045](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6045), [CVE-2018-6046](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6046), [CVE-2018-6047](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6047), [CVE-2018-6048](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6048), [CVE-2017-6049](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-6049), [CVE-2018-6050](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6050), [CVE-2018-6051](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6051), [CVE-2018-6052](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6052), [CVE-2018-6053](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6053), [CVE-2018-6054](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6054), [CVE-2017-15420](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15420) |
| AOSA-2018-0066 | Update MPV to 0.27.0-3 | [CVE-2018-6360](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6360) |
| AOSA-2018-0067 | Update Exim to 4.90.1 | [CVE-2018-6789](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6789) |
| AOSA-2018-0068 | Update Flash Player PPAPI Plugin to 28.0.0.161 | [CVE-2018-4877](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4877), [CVE-2018-4878](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4878) |
| AOSA-2018-0069 | Update Django to 2.0.2 | [CVE-2018-6188](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6188) |
| AOSA-2018-0070 | Update LibTIFF to 4.0.9-1 | [CVE-2017-17095](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-17095), [CVE-2017-18013](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-18013) |
| AOSA-2018-0071 | Update UnZip to 6.10c23 | [CVE-2018-1000031](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1000031), [CVE-2018-1000032](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1000032), [CVE-2018-1000033](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1000033), [CVE-2018-1000034](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1000034), [CVE-2018-1000035](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1000035) |
| AOSA-2018-0072 | Update KDE/Plasma Workspace to 5.11.5-1 | [CVE-2018-6791](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6791) |
| AOSA-2018-0073 | Update Go (Google) to 1.9.4 | [CVE-2018-6574](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6574) |
| AOSA-2018-0074 | Update PostgreSQL to 10.2 | [CVE-2018-1052](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1052), [CVE-2018-1053](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1053) |
| AOSA-2018-0075 | Update LibreOffice to ******* | [CVE-2018-6871](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6871) |
| AOSA-2018-0076 | Update WavPack to 5.1.0 | [CVE-2018-6767](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6767) |
| AOSA-2018-0077 | Update FreeType to 2.8.1-1 | [CVE-2018-6942](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6942) |
| AOSA-2018-0078 | Update Quagga to 1.2.3 | [CVE-2018-5378](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5378), [CVE-2018-5379](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5379), [CVE-2018-5380](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5380), [CVE-2018-5381](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5381) |
| AOSA-2018-0079 | Update Opera to 51.0.2830.34 | [CVE-2018-6031](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6031), [CVE-2018-6032](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6032), [CVE-2018-6033](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6033), [CVE-2018-6034](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6034), [CVE-2018-6035](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6035), [CVE-2018-6036](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6036), [CVE-2018-6037](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6037), [CVE-2018-6038](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6038), [CVE-2018-6039](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6039), [CVE-2018-6040](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6040), [CVE-2018-6041](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6041), [CVE-2018-6042](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6042), [CVE-2018-6043](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6043), [CVE-2018-6045](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6045), [CVE-2018-6046](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6046), [CVE-2018-6047](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6047), [CVE-2018-6048](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6048), [CVE-2017-6049](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-6049), [CVE-2018-6050](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6050), [CVE-2018-6051](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6051), [CVE-2018-6052](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6052), [CVE-2018-6053](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6053), [CVE-2018-6054](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6054), [CVE-2017-15420](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15420) |
| AOSA-2018-0080 | Update Vivaldi to 1.14.1077.50 |
| AOSA-2018-0081 | Update Irssi to 1.0.7 | [CVE-2018-7050](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7050), [CVE-2018-7051](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7051), [CVE-2018-7052](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7052), [CVE-2018-7053](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7053), [CVE-2018-7054](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7054) |
| AOSA-2018-0082 | Update Brave Browser to 0.20.42 |
| AOSA-2018-0083 | Update POSIX 7-Zip to 16.02-2 | [CVE-2018-5996](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5996) |
| AOSA-2018-0084 | Update MariaDB to 10.2.13 | [CVE-2018-2562](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2562), [CVE-2018-2622](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2622), [CVE-2018-2640](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2640), [CVE-2018-2665](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2665), [CVE-2018-2668](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2668), [CVE-2018-2612](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2612) |
| AOSA-2018-0085 | Update Chromium and Google Chrome to 64.0.3282.167 | [CVE-2018-6056](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6056) |
| AOSA-2018-0086 | Update Qemu to 2.11.1 | [CVE-2017-11334](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-11334), [CVE-2017-13672](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-13672), [CVE-2017-14167](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-14167), [CVE-2017-15038](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15038), [CVE-2017-15118](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15118), [CVE-2017-15119](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15119), [CVE-2017-15124](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15124), [CVE-2017-15268](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15268), [CVE-2017-15289](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15289), [CVE-2017-16845](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-16845), [CVE-2017-17381](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-17381), [CVE-2017-18043](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-18043), [CVE-2018-5683](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5683) |
| AOSA-2018-0087 | Update LibVirt to 3.9.0-2 | [CVE-2018-5748](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5748), [CVE-2018-6764](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6764) |
| AOSA-2018-0088 | Update strongSwan to 5.6.2 | [CVE-2018-6459](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6459) |
| AOSA-2018-0089 | Update Opera to 51.0.2830.40 | [CVE-2018-6056](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6056) |
| AOSA-2018-0090 | Update WavPack to 5.1.0-2 | [CVE-2018-7253](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7253), [CVE-2018-7254](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7254) |
| AOSA-2018-0091 | Update UnixODBC to 2.3.5 | [CVE-2018-7409](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7409) |
| AOSA-2018-0092 | Update Flatpak to 0.10.1-1 | [CVE-2018-6560](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6560) |
| AOSA-2018-0093 | Update .NET SDK to 2.1.4 | [CVE-2018-0764](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-0764), [CVE-2018-0786](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-0786) |
| AOSA-2018-0094 | Update .NET Runtime to 2.1.4 | [CVE-2018-0764](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-0764), [CVE-2018-0786](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-0786) |
| AOSA-2018-0095 | Update Wireshark to 2.4.5 | [CVE-2018-7320](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7320), [CVE-2018-7334](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7334), [CVE-2018-7335](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7335), [CVE-2018-7336](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7336), [CVE-2018-7337](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7337), [CVE-2018-7417](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7417), [CVE-2018-7418](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7418), [CVE-2018-7419](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7419), [CVE-2018-7420](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7420) |
| AOSA-2018-0096 | Update Xerces-C to 3.2.1 | [CVE-2017-12627](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-12627) |
| AOSA-2018-0097 | Update Dovecot to 2.2.34 | [CVE-2017-14461](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-14461), [CVE-2017-15130](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15130) |
| AOSA-2018-0098 | Update FreeXL to 1.0.5 | [CVE-2018-7435](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7435), [CVE-2018-7436](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7436), [CVE-2018-7437](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7437), [CVE-2018-7438](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7438), [CVE-2018-7439](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7439) |
| AOSA-2018-0099 | Update MIT Kerberos 5 (Krb5) to 1.15.2-1 | [CVE-2018-5710](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5710), [CVE-2018-5729](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5729), [CVE-2018-5730](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5730) |
| AOSA-2018-0100 | Update Busybox to 1.27.2-1 | [CVE-2017-16544](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-16544) |
| AOSA-2018-0101 | Update Tor to 0.3.1.10 | [CVE-2018-0491](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-0491) |
| AOSA-2018-0102 | Update PostgreSQL to 10.3 | [CVE-2018-1058](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1058) |
| AOSA-2018-0103 | Update Django to 2.0.3 | [CVE-2018-7536](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7536), [CVE-2018-7537](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7537) |
| AOSA-2018-0104 | Update MbedTLS to 2.7.0 | [CVE-2018-0487](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-0487), [CVE-2018-0488](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-0488) |
| AOSA-2018-0105 | Update DHCP to 4.3.6.P1 | [CVE-2018-5732](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5732), [CVE-2018-5733](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5733) |
| AOSA-2018-0106 | Update Ruby to 2.4.3-1 |
| AOSA-2018-0107 | Update PHP 7 to 7.2.3 | [CVE-2018-7584](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7584) |
| AOSA-2018-0108 | Update Network Time Procotol (NTP) Client to 4.2.8p11 | [CVE-2016-1549](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2016-1549), [CVE-2018-7170](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7170), [CVE-2018-7182](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7182), [CVE-2018-7184](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7184), [CVE-2018-7185](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7185) |
| AOSA-2018-0109 | Update Util-Linux to 2.31-1 | [CVE-2018-7738](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7738) |
| AOSA-2018-0110 | Update Timidity++ to 2.14.0-4 | [CVE-2017-11546](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-11546), [CVE-2017-11547](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-11547) |
| AOSA-2018-0111 | Update MPV to 0.27.2 | [CVE-2018-6360](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6360) |
| AOSA-2018-0112 | Update Zsh to 5.4.2-1 | [CVE-2018-7548](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7548), [CVE-2018-7549](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7549) |
| AOSA-2018-0113 | Update ClamAV to 0.99.4 | [CVE-2012-6706](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2012-6706), [CVE-2017-6419](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-6419), [CVE-2017-11423](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-11423), [CVE-2018-0202](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-0202), [CVE-2018-1000085](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1000085) |
| AOSA-2018-0114 | Update LibRAW to 0.18.8 | [CVE-2017-13735](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-13735), [CVE-2017-14265](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-14265) |
| AOSA-2018-0115 | Update Samba to 4.7.6 | [CVE-2018-1050](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1050), [CVE-2018-1057](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1057) |
| AOSA-2018-0116 | Update Firefox to 59.0 | [CVE-2018-5125](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5125), [CVE-2018-5126](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5126), [CVE-2018-5127](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5127), [CVE-2018-5128](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5128), [CVE-2018-5129](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5129), [CVE-2018-5130](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5130), [CVE-2018-5131](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5131), [CVE-2018-5132](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5132), [CVE-2018-5133](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5133), [CVE-2018-5134](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5134), [CVE-2018-5135](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5135), [CVE-2018-5136](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5136), [CVE-2018-5137](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5137), [CVE-2018-5140](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5140), [CVE-2018-5141](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5141), [CVE-2018-5142](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5142), [CVE-2018-5143](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5143) |
| AOSA-2018-0117 | Update Chromium and Google Chrome to 65.0.3325.162 | [CVE-2017-11215](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-11215), [CVE-2017-11225](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-11225), [CVE-2018-6057](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6057), [CVE-2018-6060](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6060), [CVE-2018-6061](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6061), [CVE-2018-6062](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6062), [CVE-2018-6063](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6063), [CVE-2018-6064](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6064), [CVE-2018-6065](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6065), [CVE-2018-6066](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6066), [CVE-2018-6067](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6067), [CVE-2018-6068](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6068), [CVE-2018-6069](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6069), [CVE-2018-6070](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6070), [CVE-2018-6071](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6071), [CVE-2018-6072](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6072), [CVE-2018-6073](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6073), [CVE-2018-6074](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6074), [CVE-2018-6075](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6075), [CVE-2018-6076](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6076), [CVE-2018-6077](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6077), [CVE-2018-6078](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6078), [CVE-2018-6079](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6079), [CVE-2018-6080](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6080), [CVE-2018-6081](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6081), [CVE-2018-6082](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6082), [CVE-2018-6083](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6083) |
| AOSA-2018-0118 | Update cURL to 7.59.0 | [CVE-2018-1000120](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1000120), [CVE-2018-1000121](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1000121), [CVE-2018-1000122](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1000122) |
| AOSA-2018-0119 | Update Memcached to 1.5.6 | [CVE-2018-1000115](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1000115) |
| AOSA-2018-0120 | Update Firefox to 59.0.1 | [CVE-2018-5146](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5146), [CVE-2018-5147](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5147) |
| AOSA-2018-0121 | Update .NET Runtime to 2.0.6 | [CVE-2018-0875](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-0875) |
| AOSA-2018-0122 | Update .NET SDK to 2.1.101 | [CVE-2018-0875](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-0875) |
| AOSA-2018-0123 | Update Flash Player PepperAPI Plugin to 29.0.0.113 | [CVE-2018-4919](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4919), [CVE-2018-4920](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4920) |
| AOSA-2018-0124 | Update Ceph to 12.2.2-1 | [CVE-2018-7262](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7262) |
| AOSA-2018-0125 | Update Commons-Compress to 1.16.1 | [CVE-2018-1324](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1324) |
| AOSA-2018-0126 | Update LibVorbis to 1.3.6 | [CVE-2018-5146](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5146) |
| AOSA-2018-0127 | Update SQLite to 3.21.0-1 | [CVE-2018-8740](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-8740) |
| AOSA-2018-0128 | Update SDL2-Image to 2.0.1-1 | [CVE-2017-2887](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-2887) |
| AOSA-2018-0129 | Update PyCrypto to 2.6.1-4 | [CVE-2018-6594](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6594) |
| AOSA-2018-0130 | Update Sharutils to 4.15.2-1 | [CVE-2018-1000097](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1000097) |
| AOSA-2018-0131 | Update LibTIFF to 4.0.9-2 | [CVE-2018-5784](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5784) |
| AOSA-2018-0132 | Update Paramiko to 2.1.5 | [CVE-2018-7750](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7750) |
| AOSA-2018-0133 | Update Brave Browser to 0.21.24 | [CVE-2017-11215](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-11215), [CVE-2017-11225](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-11225), [CVE-2018-6057](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6057), [CVE-2018-6060](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6060), [CVE-2018-6061](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6061), [CVE-2018-6062](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6062), [CVE-2018-6063](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6063), [CVE-2018-6064](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6064), [CVE-2018-6065](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6065), [CVE-2018-6066](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6066), [CVE-2018-6067](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6067), [CVE-2018-6068](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6068), [CVE-2018-6069](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6069), [CVE-2018-6070](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6070), [CVE-2018-6071](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6071), [CVE-2018-6072](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6072), [CVE-2018-6073](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6073), [CVE-2018-6074](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6074), [CVE-2018-6075](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6075), [CVE-2018-6076](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6076), [CVE-2018-6077](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6077), [CVE-2018-6078](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6078), [CVE-2018-6079](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6079), [CVE-2018-6080](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6080), [CVE-2018-6081](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6081), [CVE-2018-6082](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6082), [CVE-2018-6083](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6083) |
| AOSA-2018-0134 | Update Chromium and Google Chrome to 65.0.3325.181 |
| AOSA-2018-0135 | Update Pale Moon to 27.8.2 | [CVE-2018-5129](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5129), [CVE-2018-5137](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5137) |
| AOSA-2018-0136 | Update Thunderbird to 52.7.0 | [CVE-2018-5125](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5125), [CVE-2018-5127](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5127), [CVE-2018-5129](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5129), [CVE-2018-5144](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5144), [CVE-2018-5145](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5145), [CVE-2018-5146](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5146) |
| AOSA-2018-0137 | Update Apache HTTPD to 2.4.33 | [CVE-2017-15710](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15710), [CVE-2017-15715](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15715), [CVE-2018-1283](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1283), [CVE-2018-1301](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1301), [CVE-2018-1302](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1302), [CVE-2018-1303](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1303), [CVE-2018-1312](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1312) |
| AOSA-2018-0138 | Update PLIB to 1.8.5-1 | [CVE-2011-4620](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2011-4620), [CVE-2012-4552](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2012-4552) |
| AOSA-2018-0139 | Update Busybox to 1.27.2-2 | [CVE-2017-15873](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15873), [CVE-2017-15874](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15874) |
| AOSA-2018-0140 | Update Exempi to 2.4.5 | [CVE-2018-7728](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7728), [CVE-2018-7729](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7729), [CVE-2018-7730](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7730), [CVE-2018-7731](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7731) |
| AOSA-2018-0141 | Update Opera to 52.0.2871.37 |
| AOSA-2018-0142 | Update MuPDF to 1.11-4 | [CVE-2018-6544](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6544), [CVE-2018-1000051](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1000051) |
| AOSA-2018-0143 | Update Firefox to 59.0.2 | [CVE-2018-5148](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5148) |
| AOSA-2018-0144 | Update Zsh to 5.4.2-2 | [CVE-2018-1071](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1071), [CVE-2018-1083](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1083) |
| AOSA-2018-0145 | Update OpenSSL to 1.0.2o | [CVE-2018-0739](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-0739) |
| AOSA-2018-0146 | Update Intel Microcode to 20180312 | [CVE-2017-5753](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5753), [CVE-2017-5715](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5715), [CVE-2017-5754](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5754) |
| AOSA-2018-0147 | Revoked |
| AOSA-2018-0148 | Update Python (version 3) to 3.6.5 | [CVE-2018-1060](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1060), [CVE-2018-1061](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1061) |
| AOSA-2018-0149 | Update PHP to 7.2.4 |
| AOSA-2018-0150 | Update LibVirt to 3.9.0-3 | [CVE-2018-1064](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1064) |
| AOSA-2018-0151 | Update Node.js to 9.10.0 | [CVE-2018-7158](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7158), [CVE-2018-7159](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7159), [CVE-2018-7160](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7160) |
| AOSA-2018-0152 | Update Net-SNMP to 5.7.3-5 | [CVE-2015-5621](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2015-5621), [CVE-2018-1000116](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1000116) |
| AOSA-2018-0153 | Update Ruby to 2.4.4 | [CVE-2017-17742](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-17742), [CVE-2018-6914](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6914), [CVE-2018-8777](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-8777), [CVE-2018-8778](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-8778), [CVE-2018-8779](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-8779), [CVE-2018-8780](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-8780) |
| AOSA-2018-0154 | Update Aubio to 0.4.6-1 | [CVE-2017-17054](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-17054) |
| AOSA-2018-0155 | Update CFITSIO to 3.430 |
| AOSA-2018-0156 | Update SPICE vdagent to 0.17.0-1 | [CVE-2017-15108](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15108) |
| AOSA-2018-0157 | Update ZZIPlib to 0.13.69 | [CVE-2017-5977](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5977), [CVE-2017-5978](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5978), [CVE-2018-7725](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7725), [CVE-2018-7726](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7726), [CVE-2018-7727](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7727) |
| AOSA-2018-0158 | Update LibVNCServer to 0.9.10-4 | [CVE-2018-7225](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7225) |
| AOSA-2018-0159 | Update SeaMonkey to 2.49.2 | [CVE-2017-7793](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-7793), [CVE-2017-7805](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-7805), [CVE-2017-7810](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-7810), [CVE-2017-7814](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-7814), [CVE-2017-7818](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-7818), [CVE-2017-7819](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-7819), [CVE-2017-7823](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-7823), [CVE-2017-7824](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-7824), [CVE-2017-7825](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-7825), [CVE-2018-5089](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5089), [CVE-2018-5091](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5091), [CVE-2018-5095](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5095), [CVE-2018-5096](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5096), [CVE-2018-5097](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5097), [CVE-2018-5098](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5098), [CVE-2018-5099](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5099), [CVE-2018-5102](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5102), [CVE-2018-5103](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5103), [CVE-2018-5104](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5104), [CVE-2018-5117](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5117) |
| AOSA-2018-0160 | Update Go to 1.8.7 | [CVE-2018-6574](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6574) |
| AOSA-2018-0161 | Update Patch to 2.7.6 | [CVE-2016-10713](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2016-10713), [CVE-2018-1000156](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1000156), [CVE-2018-6951](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6951) |
| AOSA-2018-0162 | Update Flash Player PepperAPI Plugin to 29.0.0.140 | [CVE-2018-4932](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4932), [CVE-2018-4933](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4933), [CVE-2018-4934](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4934), [CVE-2018-4935](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4935), [CVE-2018-4936](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4936), [CVE-2018-4937](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4937) |
| AOSA-2018-0163 | Update LibiCal to 3.0.3 |
| AOSA-2018-0164 | Update OpenOCD to 0.10.0-1 | [CVE-2018-5704](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5704) |
| AOSA-2018-0165 | Update Docker to 18.02.0 | [CVE-2017-16539](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-16539), [CVE-2017-14992](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-14992) |
| AOSA-2018-0166 | Update KDE Plasma Workspace to 5.12.4 | [CVE-2018-6790](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6790), [CVE-2018-6791](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6791) |
| AOSA-2018-0167 | Update LibreOffice to 6.0.1.1 | [CVE-2018-6871](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6871) |
| AOSA-2018-0168 | Update QPDF to 7.1.1 |
| AOSA-2018-0169 | Update Calibre to 3.19.0 | [CVE-2018-7889](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7889) |
| AOSA-2018-0170 | Update Busybox to 1.28.2 | [CVE-2017-15873](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15873), [CVE-2017-15874](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15874) |
| AOSA-2018-0171 | Update MuPDF to 1.12.0 | [CVE-2018-6544](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6544), [CVE-2018-1000051](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1000051) |
| AOSA-2018-0172 | Update WebKitGTK+ to 2.20.0 | [CVE-2018-4101](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4101), [CVE-2018-4113](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4113), [CVE-2018-4114](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4114), [CVE-2018-4117](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4117), [CVE-2018-4118](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4118), [CVE-2018-4119](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4119), [CVE-2018-4120](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4120), [CVE-2018-4122](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4122), [CVE-2018-4125](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4125), [CVE-2018-4127](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4127), [CVE-2018-4128](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4128), [CVE-2018-4129](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4129), [CVE-2018-4133](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4133), [CVE-2018-4146](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4146), [CVE-2018-4161](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4161), [CVE-2018-4162](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4162), [CVE-2018-4163](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4163), [CVE-2018-4165](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4165) |
| AOSA-2018-0173 | Update Nmap to 7.70 |
| AOSA-2018-0174 | Update NgHTTP2 to 1.31.1 | [CVE-2018-1000168](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1000168) |
| AOSA-2018-0175 | Update Go to 1.10.1 | [CVE-2018-7187](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7187) |
| AOSA-2018-0176 | Update GNU C Library (glibc) to 1:2.27 | [CVE-2009-5064](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2009-5064), [CVE-2017-15670](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15670), [CVE-2017-15671](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15671), [CVE-2017-15804](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15804), [CVE-2017-16997](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-16997), [CVE-2017-17426](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-17426), [CVE-2017-1000408](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-1000408), [CVE-2017-1000409](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-1000409), [CVE-2018-6485](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6485), [CVE-2018-6551](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6551), [CVE-2018-1000001](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1000001) |
| AOSA-2018-0177 | Update Binutils to 2.30.1 | [CVE-2017-15938](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15938), [CVE-2017-15939](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15939) |
| AOSA-2018-0178 | Update Perl to 5.24.3-1 | [CVE-2018-6797](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6797), [CVE-2018-6798](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6798), [CVE-2018-6913](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6913) |
| AOSA-2018-0179 | Update LibreOffice to 6.0.2.1 | [CVE-2018-10120](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10120) |
| AOSA-2018-0180 | Update Zsh to 5.4.2-3 | [CVE-2018-1100](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1100) |
| AOSA-2018-0181 | Update OpenSSL to 1.0.2o-1 | [CVE-2018-0737](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-0737) |
| AOSA-2018-0182 | Update Krb5 to 1.16-1 | [CVE-2018-5710](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5710), [CVE-2018-5729](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5729), [CVE-2018-5730](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5730) |
| AOSA-2018-0183 | Update SQLite to 3.22.0-1 | [CVE-2018-8740](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-8740) |
| AOSA-2018-0184 | Update LibVirt to 4.1.0-1 | [CVE-2018-1064](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1064) |
| AOSA-2018-0185 | Update CFITSIO to 3.440 |
| AOSA-2018-0186 | Update Squid to 3.5.27 |
| AOSA-2018-0187 | Update VirtualBox to 5.2.10 | [CVE-2018-0739](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-0739), [CVE-2018-2831](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2831), [CVE-2018-2842](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2842), [CVE-2018-2843](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2843), [CVE-2018-2844](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2844), [CVE-2018-2845](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2845), [CVE-2018-2830](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2830), [CVE-2018-2835](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2835), [CVE-2018-2836](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2836), [CVE-2018-2837](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2837), [CVE-2018-2860](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2860) |
| AOSA-2018-0188 | Update SDL2-Image to 2.0.1-2 | [CVE-2017-12122](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-12122), [CVE-2017-14440](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-14440), [CVE-2017-14441](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-14441), [CVE-2017-14442](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-14442), [CVE-2017-14448](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-14448), [CVE-2017-14449](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-14449), [CVE-2017-14450](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-14450), [CVE-2018-3837](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3837), [CVE-2018-3838](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3838), [CVE-2018-3839](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3839) |
| AOSA-2018-0189 | Update LibTIFF to 4.0.9-3 | [CVE-2018-7456](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7456) |
| AOSA-2018-0190 | Update OpenJDK to 8u172b11 | [CVE-2018-2783](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2783), [CVE-2018-2794](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2794), [CVE-2018-2795](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2795), [CVE-2018-2796](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2796), [CVE-2018-2797](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2797), [CVE-2018-2798](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2798), [CVE-2018-2799](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2799), [CVE-2018-2790](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2790), [CVE-2018-2800](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2800), [CVE-2018-2811](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2811), [CVE-2018-2814](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2814), [CVE-2018-2815](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2815), [CVE-2018-2825](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2825), [CVE-2018-2826](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2826) |
| AOSA-2018-0191 | Update LibCDIO to 0.94-2 | [CVE-2017-18198](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-18198), [CVE-2017-18199](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-18199), [CVE-2017-18201](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-18201) |
| AOSA-2018-0192 | Update Google Chrome to 66.0.3359.117 | [CVE-2018-6085](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6085), [CVE-2018-6086](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6086), [CVE-2018-6087](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6087), [CVE-2018-6088](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6088), [CVE-2018-6089](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6089),[CVE-2018-6090](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6090), [CVE-2018-6091](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6091), [CVE-2018-6092](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6092), [CVE-2018-6093](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6093), [CVE-2018-6094](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6094), [CVE-2018-6095](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6095), [CVE-2018-6096](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6096), [CVE-2018-6097](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6097), [CVE-2018-6098](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6098), [CVE-2018-6099](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6099), [CVE-2018-6100](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6100), [CVE-2018-6101](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6101), [CVE-2018-6102](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6102), [CVE-2018-6103](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6103), [CVE-2018-6104](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6104), [CVE-2018-6105](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6105), [CVE-2018-6106](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6106), [CVE-2018-6107](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6107), [CVE-2018-6108](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6108), [CVE-2018-6109](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6109), [CVE-2018-6110](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6110), [CVE-2018-6111](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6111), [CVE-2018-6112](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6112), [CVE-2018-6113](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6113), [CVE-2018-6114](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6114), [CVE-2018-6115](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6115), [CVE-2018-6116](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6116), [CVE-2018-6117](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6117) |
| AOSA-2018-0193 | Update PackageKit to 1.1.10 | [CVE-2018-1106](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1106) |
| AOSA-2018-0194 | Update FFmpeg to 3.4.2-1 | [CVE-2018-7751](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7751) |
| AOSA-2018-0195 | Update Vivaldi to 1.15.1147.36 | [CVE-2017-11215](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-11215), [CVE-2017-11225](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-11225), [CVE-2018-6057](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6057), [CVE-2018-6060](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6060), [CVE-2018-6061](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6061), [CVE-2018-6062](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6062), [CVE-2018-6063](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6063), [CVE-2018-6064](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6064), [CVE-2018-6065](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6065), [CVE-2018-6066](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6066), [CVE-2018-6067](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6067), [CVE-2018-6068](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6068), [CVE-2018-6069](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6069), [CVE-2018-6070](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6070), [CVE-2018-6071](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6071), [CVE-2018-6072](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6072), [CVE-2018-6073](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6073), [CVE-2018-6074](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6074), [CVE-2018-6075](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6075), [CVE-2018-6076](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6076), [CVE-2018-6077](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6077), [CVE-2018-6078](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6078), [CVE-2018-6079](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6079), [CVE-2018-6080](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6080), [CVE-2018-6081](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6081), [CVE-2018-6082](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6082), [CVE-2018-6083](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6083) |
| AOSA-2018-0196 | Update Brave Browser to 0.22.669dev | [CVE-2018-6085](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6085), [CVE-2018-6086](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6086), [CVE-2018-6087](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6087), [CVE-2018-6088](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6088), [CVE-2018-6089](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6089),[CVE-2018-6090](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6090), [CVE-2018-6091](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6091), [CVE-2018-6092](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6092), [CVE-2018-6093](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6093), [CVE-2018-6094](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6094), [CVE-2018-6095](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6095), [CVE-2018-6096](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6096), [CVE-2018-6097](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6097), [CVE-2018-6098](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6098), [CVE-2018-6099](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6099), [CVE-2018-6100](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6100), [CVE-2018-6101](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6101), [CVE-2018-6102](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6102), [CVE-2018-6103](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6103), [CVE-2018-6104](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6104), [CVE-2018-6105](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6105), [CVE-2018-6106](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6106), [CVE-2018-6107](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6107), [CVE-2018-6108](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6108), [CVE-2018-6109](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6109), [CVE-2018-6110](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6110), [CVE-2018-6111](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6111), [CVE-2018-6112](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6112), [CVE-2018-6113](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6113), [CVE-2018-6114](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6114), [CVE-2018-6115](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6115), [CVE-2018-6116](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6116), [CVE-2018-6117](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6117) |
| AOSA-2018-0197 | Update Quassel to 0.12.5 | [CVE-2018-1000178](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1000178), [CVE-2018-1000179](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1000179) |
| AOSA-2018-0198 | Update Google Chrome to 66.0.3359.139 | [CVE-2018-6118](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6118) |
| AOSA-2018-0199 | Update Chromium to 66.0.3359.139 | [CVE-2018-6085](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6085), [CVE-2018-6086](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6086), [CVE-2018-6087](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6087), [CVE-2018-6088](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6088), [CVE-2018-6089](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6089),[CVE-2018-6090](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6090), [CVE-2018-6091](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6091), [CVE-2018-6092](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6092), [CVE-2018-6093](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6093), [CVE-2018-6094](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6094), [CVE-2018-6095](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6095), [CVE-2018-6096](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6096), [CVE-2018-6097](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6097), [CVE-2018-6098](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6098), [CVE-2018-6099](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6099), [CVE-2018-6100](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6100), [CVE-2018-6101](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6101), [CVE-2018-6102](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6102), [CVE-2018-6103](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6103), [CVE-2018-6104](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6104), [CVE-2018-6105](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6105), [CVE-2018-6106](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6106), [CVE-2018-6107](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6107), [CVE-2018-6108](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6108), [CVE-2018-6109](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6109), [CVE-2018-6110](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6110), [CVE-2018-6111](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6111), [CVE-2018-6112](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6112), [CVE-2018-6113](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6113), [CVE-2018-6114](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6114), [CVE-2018-6115](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6115), [CVE-2018-6116](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6116), [CVE-2018-6117](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6117), [CVE-2018-6118](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6118) |
| AOSA-2018-0200 | Update PHP to 7.2.5 | [CVE-2018-5712](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5712) |
| AOSA-2018-0201 | Update KTextEditor to 5.44.0-1 | [CVE-2018-10361](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10361) |
| AOSA-2018-0202 | Update GhostScript to 9.23-1 | [CVE-2018-10194](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10194) |
| AOSA-2018-0203 | Update GhostPDL to 9.23-1 | [CVE-2018-10194](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10194) |
| AOSA-2018-0204 | Update SoX to 14.4.2-3 | [CVE-2017-11332](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-11332), [CVE-2017-11358](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-11358), [CVE-2017-11359](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-11359), [CVE-2017-15372](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15372), [CVE-2017-15642](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-15642), [CVE-2017-18189](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-18189) |
| AOSA-2018-0205 | Update WavPack to 5.1.0-3 | [CVE-2018-10536](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10536), [CVE-2018-10537](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10537), [CVE-2018-10538](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10538), [CVE-2018-10539](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10539), [CVE-2018-10540](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10540) |
| AOSA-2018-0206 | Update GraphicsMagick to 1.3.29 |
| AOSA-2018-0207 | Update SeaMonkey to 2.49.3 | [CVE-2018-5125](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5125), [CVE-2018-5127](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5127), [CVE-2018-5129](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5129), [CVE-2018-5144](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5144), [CVE-2018-5145](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5145), [CVE-2018-5146](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5146), [CVE-2018-5148](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5148) |
| AOSA-2018-0208 | Update Transmission to 2.94 | [CVE-2018-5702](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5702) |
| AOSA-2018-0209 | Update Boost to 1.63.0-3 |
| AOSA-2018-0210 | Update OpenVPN to 2.4.6 | [CVE-2018-9336](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-9336) |
| AOSA-2018-0211 | Update GNU wget to 1.19.5 | [CVE-2018-0494](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-0494) |
| AOSA-2018-0212 | Update WebKit2GTK+ to 2.20.2 | [CVE-2018-4200](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4200), [CVE-2018-4204](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4204) |
| AOSA-2018-0213 | Update QPdf to 7.1.1-1 | [CVE-2018-9918](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-9918) |
| AOSA-2018-0214 | Update LibRAW to 0.18.10 | [CVE-2018-10528](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10528), [CVE-2018-10529](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10529) |
| AOSA-2018-0215 | Update Linux Kernel to 4.15.18-1 | [CVE-2018-1108](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1108) |
| AOSA-2018-0216 | Update Linux Kernel (Long-Term Support Branch) to 4.14.39-1 | [CVE-2017-18216](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-18216), [CVE-2017-18224](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-18224) |
| AOSA-2018-0217 | Update VLC to 3.0.1 | [CVE-2017-17670](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-17670) |
| AOSA-2018-0218 | Update MariaDB to 10.2.15 | [CVE-2018-2786](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2786), [CVE-2018-2759](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2759), [CVE-2018-2777](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2777), [CVE-2018-2810](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2810), [CVE-2018-2782](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2782), [CVE-2018-2784](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2784), [CVE-2018-2787](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2787), [CVE-2018-2766](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2766), [CVE-2018-2755](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2755), [CVE-2018-2819](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2819), [CVE-2018-2817](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2817), [CVE-2018-2761](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2761), [CVE-2018-2781](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2781), [CVE-2018-2771](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2771), [CVE-2018-2813](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2813) |
| AOSA-2018-0219 | Update Flash Player Plugin to 29.0.0.171 | [CVE-2018-4944](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4944) |
| AOSA-2018-0220 | Update Firefox to 60.0.1 | [CVE-2018-5150](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5150), [CVE-2018-5151](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5151), [CVE-2018-5152](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5152), [CVE-2018-5153](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5153), [CVE-2018-5154](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5154), [CVE-2018-5155](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5155), [CVE-2018-5157](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5157), [CVE-2018-5158](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5158), [CVE-2018-5159](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5159), [CVE-2018-5160](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5160), [CVE-2018-5163](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5163), [CVE-2018-5164](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5164), [CVE-2018-5165](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5165), [CVE-2018-5166](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5166), [CVE-2018-5167](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5167), [CVE-2018-5168](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5168), [CVE-2018-5169](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5169), [CVE-2018-5172](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5172), [CVE-2018-5173](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5173), [CVE-2018-5174](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5174), [CVE-2018-5175](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5175), [CVE-2018-5176](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5176), [CVE-2018-5177](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5177), [CVE-2018-5180](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5180), [CVE-2018-5181](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5181), [CVE-2018-5182](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5182) |
| AOSA-2018-0221 | Update FLAC to 1.3.2-2 | [CVE-2017-6888](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-6888) |
| AOSA-2018-0222 | Update LibID3Tag to 0.15.1b-3 | [CVE-2004-2779](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2004-2779), [CVE-2017-11550](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-11550) |
| AOSA-2018-0223 | Update Opera to 53.0.2907.57 | [CVE-2018-6118](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6118) |
| AOSA-2018-0224 | Update Mozilla JavaScript Core to 52.8.0 | [CVE-2018-5125](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5125), [CVE-2018-5129](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5129), [CVE-2018-5145](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5145), [CVE-2018-5150](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5150) |
| AOSA-2018-0225 | Update MuPDF to 1.13.0 | [CVE-2018-5686](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5686), [CVE-2018-6187](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6187), [CVE-2018-6192](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6192) |
| AOSA-2018-0226 | Update Chromium and Google Chrome to 66.0.3359.170 | [CVE-2018-6120](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6120), [CVE-2018-6121](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6121), [CVE-2018-6122](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6122) |
| AOSA-2018-0227 | Update AffLib to 3.7.16-1 | [CVE-2018-8050](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-8050) |
| AOSA-2018-0228 | Update ImageMagick to 6.9.9+46 |
| AOSA-2018-0229 | Update KWallet-PAM to 5.12.4-1 | [CVE-2018-10380](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10380) |
| AOSA-2018-0230 | Update HAProxy to 1.8.8 | [CVE-2018-10184](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10184) |
| AOSA-2018-0231 | Update LibTIFF to 4.0.9-4 | [CVE-2018-8905](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-8905), [CVE-2018-10963](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10963) |
| AOSA-2018-0232 | Update LibRAW to 0.8.11 | [CVE-2017-13735](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-13735), [CVE-2017-14265](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-14265), [CVE-2018-10528](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10528), [CVE-2018-10529](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10529) |
| AOSA-2018-0233 | Update Graphite to 1.3.11-1 | [CVE-2018-7999](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7999) |
| AOSA-2018-0234 | Update LibSndFile to 1.0.28-1 | [CVE-2017-6892](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-6892), [CVE-2017-14245](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-14245), [CVE-2017-14246](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-14246), [CVE-2017-14634](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-14634) |
| AOSA-2018-0235 | Update cURL to 7.60.0 | [CVE-2018-1000300](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1000300), [CVE-2018-1000301](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1000301) |
| AOSA-2018-0236 | Update Vivaldi to 1.15.1147.42 | [CVE-2018-6085](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6085), [CVE-2018-6086](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6086), [CVE-2018-6087](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6087), [CVE-2018-6088](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6088), [CVE-2018-6089](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6089),[CVE-2018-6090](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6090), [CVE-2018-6091](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6091), [CVE-2018-6092](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6092), [CVE-2018-6093](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6093), [CVE-2018-6094](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6094), [CVE-2018-6095](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6095), [CVE-2018-6096](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6096), [CVE-2018-6097](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6097), [CVE-2018-6098](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6098), [CVE-2018-6099](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6099), [CVE-2018-6100](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6100), [CVE-2018-6101](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6101), [CVE-2018-6102](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6102), [CVE-2018-6103](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6103), [CVE-2018-6104](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6104), [CVE-2018-6105](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6105), [CVE-2018-6106](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6106), [CVE-2018-6107](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6107), [CVE-2018-6108](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6108), [CVE-2018-6109](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6109), [CVE-2018-6110](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6110), [CVE-2018-6111](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6111), [CVE-2018-6112](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6112), [CVE-2018-6113](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6113), [CVE-2018-6114](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6114), [CVE-2018-6115](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6115), [CVE-2018-6116](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6116), [CVE-2018-6117](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6117), [CVE-2018-6118](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6118) |
| AOSA-2018-0237 | Update procps (-ng) to 3.3.15 | [CVE-2018-1120](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1120), [CVE-2018-1122](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1122), [CVE-2018-1123](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1123), [CVE-2018-1124](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1124) |
| AOSA-2018-0238 | Update BIND to 9.12.1.P2 | [CVE-2018-5736](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5736), [CVE-2018-5737](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5737) |
| AOSA-2018-0239 | Update Pale Moon to 27.9.2 | [CVE-2018-5154](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5154), [CVE-2018-5159](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5159), [CVE-2018-5173](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5173), [CVE-2018-5174](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5174), [CVE-2018-5177](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5177), [CVE-2018-5178](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5178) |
| AOSA-2018-0240 | Update Thunderbird to 52.8.0 | [CVE-2018-5150](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5150), [CVE-2018-5154](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5154), [CVE-2018-5155](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5155), [CVE-2018-5159](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5159), [CVE-2018-5161](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5161), [CVE-2018-5162](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5162), [CVE-2018-5168](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5168), [CVE-2018-5170](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5170), [CVE-2018-5174](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5174), [CVE-2018-5178](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5178), [CVE-2018-5183](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5183), [CVE-2018-5184](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5184), [CVE-2018-5185](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5185) |
| AOSA-2018-0241 | Update XDG-Utils to 1.1.1-4 | [CVE-2017-18266](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-18266) |
| AOSA-2018-0242 | Update Shadow to 4.6 | [CVE-2018-7169](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7169) |
| AOSA-2018-0243 | Update Batik to 1.10 | [CVE-2018-8013](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-8013) |
| AOSA-2018-0244 | Update Python 2 to 2.7.14-3 | [CVE-2018-1000030](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1000030) |
| AOSA-2018-0245 | Update Tor to 0.3.6.6 |
| AOSA-2018-0246 | Update Intel Microcode to 20180425 | [CVE-2017-5753](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5753), [CVE-2017-5715](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5715), [CVE-2017-5754](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5754) |
| AOSA-2018-0247 | Update SPICE to 0.14.0-1 | [CVE-2017-12194](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-12194) |
| AOSA-2018-0248 | Update Brave Browser to 0.22.721dev | [CVE-2018-6120](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6120), [CVE-2018-6121](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6121), [CVE-2018-6122](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6122) |
| AOSA-2018-0249 | Update GnuPG to 2.2.5-1 | [CVE-2018-9234](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-9234) |
| AOSA-2018-0250 | Update Wireshark to 2.6.1 |
| AOSA-2018-0251 | Update PHP to 7.2.6 |
| AOSA-2018-0252 | Update JasPer to 2.0.14-1 | [CVE-2018-9055](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-9055) |
| AOSA-2018-0253 | Update Strongswan to 5.6.3 | [CVE-2018-5388](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5388), [CVE-2018-10811](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10811) |
| AOSA-2018-0254 | Update Git to 2.17.1 | [CVE-2018-11235](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-11235) |
| AOSA-2018-0255 | Update Chromium and Google Chrome to 67.0.3396.62 | [CVE-2018-6123](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6123), [CVE-2018-6124](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6124), [CVE-2018-6125](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6125), [CVE-2018-6126](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6126), [CVE-2018-6127](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6127), [CVE-2018-6128](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6128), [CVE-2018-6129](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6129), [CVE-2018-6130](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6130), [CVE-2018-6131](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6131), [CVE-2018-6132](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6132), [CVE-2018-6133](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6133), [CVE-2018-6134](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6134), [CVE-2018-6135](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6135), [CVE-2018-6136](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6136), [CVE-2018-6137](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6137), [CVE-2018-6138](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6138), [CVE-2018-6139](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6139), [CVE-2018-6140](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6140), [CVE-2018-6141](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6141), [CVE-2018-6142](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6142), [CVE-2018-6143](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6143), [CVE-2018-6144](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6144), [CVE-2018-6145](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6145), [CVE-2018-6147](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6147) |
| AOSA-2018-0256 | Update HAProxy to 1.8.8-1 | [CVE-2018-11469](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-11469) |
| AOSA-2018-0257 | Update Linux Firmwares (firmware-free, firmware-nonfree) to 20180525 | [CVE-2017-5753](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5753), [CVE-2017-5715](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5715), [CVE-2017-5754](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-5754) |
| AOSA-2018-0258 | Update Poppler to 0.60.1-2 | [CVE-2017-18267](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-18267) |
| AOSA-2018-0259 | Update LibYTNEF to 1.9.2-1 | [CVE-2017-9058](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-9058), [CVE-2017-9146](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-9146), and [CVE-2017-12141](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-12141) |
| AOSA-2018-0260 | Update LibreOffice to 6.0.4.2 | [CVE-2018-10583](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10583) |
| AOSA-2018-0261 | Update Radare2 to 2.6.0 | [CVE-2018-11375](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-11375), [CVE-2018-11376](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-11376), [CVE-2018-11377](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-11377), [CVE-2018-11378](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-11378), [CVE-2018-11379](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-11379), [CVE-2018-11380](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-11380), [CVE-2018-11381](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-11381), [CVE-2018-11382](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-11382), [CVE-2018-11383](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-11383), [CVE-2018-11384](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-11384) |
| AOSA-2018-0262 | Update Chromium and Google Chrome to 67.0.3396.79 | [CVE-2018-6148](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6148) |
| AOSA-2018-0263 | Update GnuPG to 2.2.8 | [CVE-2018-12020](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12020) |
| AOSA-2018-0264 | Update Firefox to 60.0.2 | [CVE-2018-6126](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6126) |
| AOSA-2018-0265 | Update LibGit2 to 0.26.4 | [CVE-2018-11235](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-11235) |
| AOSA-2018-0266 | Update Flash Player PepperAPI Plugin to ********** | [CVE-2018-4945](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4945), [CVE-2018-5000](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5000), [CVE-2018-5001](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5001), [CVE-2018-5002](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5002) |
| AOSA-2018-0267 | Update POSIX 7-Zip (p7zip) to 16.02-3 | [CVE-2018-10115](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10115) |
| AOSA-2018-0268 | Update PPP to 2.4.7-2 | [CVE-2018-11574](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-11574) |
| AOSA-2018-0269 | Update ImageMagick to 6.9.9+51 |
| AOSA-2018-0270 | Update LibVorbis to 1.3.6-1 | [CVE-2018-10392](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10392) |
| AOSA-2018-0271 | Update Perl to 5.24.3-2 | [CVE-2018-6798](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6798), [CVE-2018-12015](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12015) |
| AOSA-2018-0272 | Update GNU C Library (Glibc) to 2.27-1 | [CVE-2018-11237](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-11237) |
| AOSA-2018-0273 | Update Node.js to 9.11.2 | [CVE-2018-7162](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7162), [CVE-2018-7164](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7164), [CVE-2018-7167](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7167), [CVE-2018-1000168](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1000168) |
| AOSA-2018-0274 | Update Linux Kernel LTS (linux+kernel+lts) to 4.14.49 | [CVE-2018-5814](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5814) |
| AOSA-2018-0275 | Update LibGcrypt to 1.8.3 | [CVE-2018-0495](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-0495) |
| AOSA-2018-0276 | Update LibYTNEF to 1.9.2-1 | [CVE-2017-9471](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-9471), [CVE-2017-9473](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-9473) |
| AOSA-2018-0277 | Update WebKit2GTK+ to 2.20.3 | [CVE-2018-4190](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4190), [CVE-2018-4199](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4199), [CVE-2018-4218](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4218), [CVE-2018-4222](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4222), [CVE-2018-4232](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4232), [CVE-2018-4233](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4233), [CVE-2018-11646](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-11646), [CVE-2018-11713](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-11713), [CVE-2018-12293](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12293) |
| AOSA-2018-0278 | Update Chromium and Google Chrome to 67.0.3396.87 | [CVE-2018-6149](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6149) |
| AOSA-2018-0279 | Update File(contains libmagic)  to 5.32-1 | [CVE-2018-10360](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10360) |
| AOSA-2018-0280 | Update Opera to 53.0.2907.99 | [CVE-2018-6148](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6148) |
| AOSA-2018-0281 | Update Pale Moon to 27.9.3 | [CVE-2017-0381](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-0381) |
| AOSA-2018-0282 | Update Vivaldi to 1.15.1147.47 | [CVE-2018-6123](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6123), [CVE-2018-6124](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6124), [CVE-2018-6125](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6125), [CVE-2018-6126](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6126), [CVE-2018-6127](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6127), [CVE-2018-6128](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6128), [CVE-2018-6129](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6129), [CVE-2018-6130](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6130), [CVE-2018-6131](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6131), [CVE-2018-6132](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6132), [CVE-2018-6133](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6133), [CVE-2018-6134](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6134), [CVE-2018-6135](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6135), [CVE-2018-6136](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6136), [CVE-2018-6137](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6137), [CVE-2018-6138](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6138), [CVE-2018-6139](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6139), [CVE-2018-6140](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6140), [CVE-2018-6141](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6141), [CVE-2018-6142](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6142), [CVE-2018-6143](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6143), [CVE-2018-6144](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6144), [CVE-2018-6145](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6145), [CVE-2018-6147](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6147), [CVE-2018-6148](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6148), [CVE-2018-6149](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6149) |
| AOSA-2018-0283 | Update Redis to 4.0.10 | [CVE-2018-11218](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-11218), [CVE-2018-11219](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-11219) |
| AOSA-2018-0284 | Update PostgreSQL to 10.4 | [CVE-2018-1115](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1115) |
| AOSA-2018-0285 | Update Brave Browser to 0.22.810dev | [CVE-2018-6123](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6123), [CVE-2018-6124](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6124), [CVE-2018-6125](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6125), [CVE-2018-6126](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6126), [CVE-2018-6127](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6127), [CVE-2018-6128](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6128), [CVE-2018-6129](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6129), [CVE-2018-6130](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6130), [CVE-2018-6131](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6131), [CVE-2018-6132](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6132), [CVE-2018-6133](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6133), [CVE-2018-6134](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6134), [CVE-2018-6135](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6135), [CVE-2018-6136](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6136), [CVE-2018-6137](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6137), [CVE-2018-6138](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6138), [CVE-2018-6139](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6139), [CVE-2018-6140](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6140), [CVE-2018-6141](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6141), [CVE-2018-6142](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6142), [CVE-2018-6143](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6143), [CVE-2018-6144](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6144), [CVE-2018-6145](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6145), [CVE-2018-6147](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6147) |
| AOSA-2018-0286 | Update Aubio to 0.4.6-2 | [CVE-2017-17554](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-17554) |
| AOSA-2018-0287 | Update Qemu to 2.11.1-1 | [CVE-2018-3639](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3639) |
| AOSA-2018-0288 | Update Linux Kernel LTS (linux+kernel+lts) to 4.14.43 | [CVE-2018-3639](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3639) |
| AOSA-2018-0289 | Update Linux Kernel Libre (linux+kernel+libre) to 4.14.43 | [CVE-2018-3639](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3639) |
| AOSA-2018-0290 | Update Exiv2 to 0.26-1 | [CVE-2017-9239](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-9239), [CVE-2018-10998](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10998), [CVE-2018-11531](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-11531),  [CVE-2018-12264](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12264), [CVE-2018-12265](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12265) |
| AOSA-2018-0291 | Update LibSoup to 2.62.0-1 | [CVE-2018-12910](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12910) |
| AOSA-2018-0292 | Update Cinnamon to 3.6.6-1 | [CVE-2018-13054](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-13054) |
| AOSA-2018-0293 | Update Stunnel to 5.48 |
| AOSA-2018-0294 | Update GraphicsMagick to 1.3.30 | [CVE-2016-2317](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2016-2317) |
| AOSA-2018-0295 | Update Unix-ODBC to 2.3.6 | [CVE-2018-7485](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-7485) |
| AOSA-2018-0296 | Update Opera to 54.0.2952.41 | [CVE-2018-6149](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6149) |
| AOSA-2018-0297 | Update Mozilla JavaScript Core (js-52) to 52.9.0 | [CVE-2018-5188](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5188), [CVE-2018-12359](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12359), [CVE-2018-12360](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12360), [CVE-2018-12363](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12363), [CVE-2018-12366](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12366) |
| AOSA-2018-0298 | Update Pass (Password Manager) 1.7.1-1 | [CVE-2018-12356](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12356) |
| AOSA-2018-0299 | Update Apache Ant to 1.10.4 |
| AOSA-2018-0300 | Update Taglib to 1.11-2 | [CVE-2018-11439](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-11439) |
| AOSA-2018-0301 | Update PHP to 7.2.6-1 | [CVE-2018-12882](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12882) |
| AOSA-2018-0302 | Update NetEase Cloud Music to 1.1.0-1 |
| AOSA-2018-0303 | Update Firefox to 61.0 | [CVE-2018-5156](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5156), [CVE-2018-5186](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5186), [CVE-2018-5187](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5187), [CVE-2018-5188](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5188), [CVE-2018-12358](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12358), [CVE-2018-12359](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12359), [CVE-2018-12360](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12360), [CVE-2018-12361](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12361), [CVE-2018-12362](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12362), [CVE-2018-12363](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12363), [CVE-2018-12364](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12364), [CVE-2018-12365](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12365), [CVE-2018-12366](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12366), [CVE-2018-12367](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12367), [CVE-2018-12368](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12368), [CVE-2018-12369](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12369), [CVE-2018-12370](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12370), [CVE-2018-12371](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12371) |
| AOSA-2018-0304 | Update Thunderbird to 52.9.0 | [CVE-2018-5188](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5188), [CVE-2018-12359](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12359), [CVE-2018-12360](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12360), [CVE-2018-12362](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12362), [CVE-2018-12363](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12363), [CVE-2018-12364](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12364), [CVE-2018-12365](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12365), [CVE-2018-12366](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12366), [CVE-2018-12368](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12368), [CVE-2018-12372](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12372), [CVE-2018-12374](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12374) |
| AOSA-2018-0305 | Update Intel Microcode to 20180703 | [CVE-2018-3639](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3639), [CVE-2018-3640](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3640) |
| AOSA-2018-0306 | Update Cairo to 1.15.10-1 | [CVE-2017-9814](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-9814) |
| AOSA-2018-0307 | Update Ceph to 12.2.2-2 | [CVE-2018-10861](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10861) |
| AOSA-2018-0308 | Update Xapian Core to 1.4.6 | [CVE-2018-0499](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-0499) |
| AOSA-2018-0309 | Update cURL to 7.60.0-1 | [CVE-2018-0500](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-0500) |
| AOSA-2018-0310 | Update CouchDB to 2.1.2 | [CVE-2018-8007](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-8007) |
| AOSA-2018-0311 | Update CUPS to 2.2.7-1 |
| AOSA-2018-0312 | Update LibPNG to 1.6.34-1 | [CVE-2018-13785](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-13785) |
| AOSA-2018-0313 | Update Flash Player PepperAPI Plugin to ********** | [CVE-2018-5008](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5008), [CVE-2018-5007](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5007) |
| AOSA-2018-0314 | Update Perl Archive::Zip to 1.59-1 | [CVE-2018-10860](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10860) |
| AOSA-2018-0315 | Update OpenSLP to 2.0.0-3 | [CVE-2017-17833](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-17833) |
| AOSA-2018-0316 | Update Mutt to 1.10.1 | [CVE-2018-14349](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14349), [CVE-2018-14350](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14350), [CVE-2018-14351](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14351), [CVE-2018-14352](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14352), [CVE-2018-14353](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14353), [CVE-2018-14354](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14354), [CVE-2018-14355](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14355), [CVE-2018-14356](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14356), [CVE-2018-14357](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14357), [CVE-2018-14358](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14358), [CVE-2018-14359](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14359), [CVE-2018-14362](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14362) |
| AOSA-2018-0317 | Update Neomutt to 20180716 | [CVE-2018-14349](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14349), [CVE-2018-14350](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14350), [CVE-2018-14351](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14351), [CVE-2018-14352](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14352), [CVE-2018-14353](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14353), [CVE-2018-14354](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14354), [CVE-2018-14355](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14355), [CVE-2018-14356](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14356), [CVE-2018-14357](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14357), [CVE-2018-14358](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14358), [CVE-2018-14359](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14359), [CVE-2018-14360](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14360), [CVE-2018-14361](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14361), [CVE-2018-14362](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14362), [CVE-2018-14363](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14363) |
| AOSA-2018-0318 | Update ZNC to 1.7.1 | [CVE-2018-14055](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14055), [CVE-2018-14056](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14056) |
| AOSA-2018-0319 | Update Apache HTTPD to 2.4.34 | [CVE-2018-1333](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1333), [CVE-2018-8011](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-8011) |
| AOSA-2018-0320 | Update Pale Moon to 27.9.4 | [CVE-2018-12364](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12364), [CVE-2018-12366](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12366), [CVE-2018-12359](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12359), [CVE-2018-12360](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12360) |
| AOSA-2018-0321 | Update Thunderbird to 52.9.1 |
| AOSA-2018-0322 | Update Chromium and Google Chrome to 68.0.3440.75 | [CVE-2018-4117](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4117), [CVE-2018-6044](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6044), [CVE-2018-6153](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6153), [CVE-2018-6154](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6154), [CVE-2018-6155](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6155), [CVE-2018-6156](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6156), [CVE-2018-6157](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6157), [CVE-2018-6158](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6158), [CVE-2018-6159](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6159), [CVE-2018-6161](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6161), [CVE-2018-6162](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6162), [CVE-2018-6163](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6163), [CVE-2018-6164](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6164), [CVE-2018-6165](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6165), [CVE-2018-6166](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6166), [CVE-2018-6167](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6167), [CVE-2018-6168](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6168), [CVE-2018-6169](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6169), [CVE-2018-6170](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6170), [CVE-2018-6171](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6171), [CVE-2018-6172](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6172), [CVE-2018-6173](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6173), [CVE-2018-6174](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6174), [CVE-2018-6175](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6175), [CVE-2018-6176](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6176), [CVE-2018-6177](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6177), [CVE-2018-6178](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6178), [CVE-2018-6179](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6179) |
| AOSA-2018-0323 | Update Cryptography to 2.0.3-1 | [CVE-2018-10903](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10903) |
| AOSA-2018-0324 | Update Wireshark to 2.6.2 |
| AOSA-2018-0325 | Update Mercurial to 4.6.2 | [CVE-2018-13346](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-13346), [CVE-2018-13347](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-13347), [CVE-2018-13348](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-13348) |
| AOSA-2018-0326 | Update OpenJDK to 8u181b13 | [CVE-2018-2938](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2938), [CVE-2018-2940](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2940), [CVE-2018-2941](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2941), [CVE-2018-2942](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2942), [CVE-2018-2952](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2952), [CVE-2018-2964](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2964), [CVE-2018-2972](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2972), [CVE-2018-2973](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-2973) |
| AOSA-2018-0327 | Update SeaMonkey to 2.49.4 | [CVE-2018-5188](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5188), [CVE-2018-12359](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12359), [CVE-2018-12360](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12360), [CVE-2018-12362](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12362), [CVE-2018-12363](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12363), [CVE-2018-12364](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12364), [CVE-2018-12365](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12365), [CVE-2018-12366](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12366), [CVE-2018-12368](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12368), [CVE-2018-12372](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12372), [CVE-2018-12374](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12374) |
| AOSA-2018-0328 | Update LibMAD to 0.15.1b-4 | [CVE-2017-8372](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-8372), [CVE-2017-8373](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-8373), [CVE-2017-8374](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-8374) |
| AOSA-2018-0329 | Update OpenSSL to 1.0.2o-2 | [CVE-2018-0732](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-0732) |
| AOSA-2018-0330 | Update Django to 2.0.8 | [CVE-2018-14574](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14574) |
| AOSA-2018-0331 | Update OpenEXR to 2.2.1 | [CVE-2017-9110](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-9110), [CVE-2017-9111](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-9111), [CVE-2017-9112](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-9112), [CVE-2017-9113](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-9113), [CVE-2017-9114](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-9114), [CVE-2017-9115](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-9115), [CVE-2017-9116](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-9116) |
| AOSA-2018-0332 | Update QPDF to 8.0.2-1 | [CVE-2018-9918](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-9918) |
| AOSA-2018-0333 | Update NVIDIA Proprietary Drivers to 396.24 | [CVE-2018-6249](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6249), [CVE-2018-6253](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6253) |
| AOSA-2018-0334 | Update LibLouis to 3.6.0 | [CVE-2018-11410](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-11410), [CVE-2018-11440](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-11440), [CVE-2018-11683](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-11683), [CVE-2018-11684](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-11684) and [CVE-2018-11685](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-11685) |
| AOSA-2018-0335 | Update ImageMagick to 6.9.10+5 |
| AOSA-2018-0336 | Update Cinnamon to 3.8.7 | [CVE-2018-13054](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-13054) |
| AOSA-2018-0337 | Update PolicyKit to 0.115 | [CVE-2018-1116](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1116) |
| AOSA-2018-0338 | Update QuteBrowser to 1.4.1 | [CVE-2018-10895](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10895) |
| AOSA-2018-0339 | Update Cantata to 2.3.1-1 | [CVE-2018-12559](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12559), [CVE-2018-12560](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12560), [CVE-2018-12561](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12561), [CVE-2018-12562](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12562) |
| AOSA-2018-0340 | Update VirtualBox to 5.2.16 | [CVE-2018-3005](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3005), [CVE-2018-3055](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3055), [CVE-2018-3085](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3085), [CVE-2018-3086](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3086), [CVE-2018-3087](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3087), [CVE-2018-3088](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3088), [CVE-2018-3089](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3089), [CVE-2018-3090](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3090), [CVE-2018-3091](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3091) |
| AOSA-2018-0341 | Update FFmpeg to 4.0.2 | [CVE-2018-13300](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-13300), [CVE-2018-13301](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-13301), [CVE-2018-13302](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-13302), [CVE-2018-13303](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-13303), [CVE-2018-13304](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-13304), [CVE-2018-14394](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14394), [CVE-2018-14395](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14395) |
| AOSA-2018-0342 | Update NetworkManager-VPNC to 1.2.6 | [CVE-2018-10900](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10900) |
| AOSA-2018-0343 | Update Wesnoth to 1.14.4 | [CVE-2018-1999023](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1999023) |
| AOSA-2018-0344 | Update Rust Compiler to 1.27.2 | [CVE-2018-1000622](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1000622) |
| AOSA-2018-0345 | Update ClamAV to 0.100.1 | [CVE-2018-0360](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-0360), [CVE-2018-0361](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-0361) |
| AOSA-2018-0346 | Update Cryptography to 2.2.2-1 | [CVE-2018-10903](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10903) |
| AOSA-2018-0347 | Update LibVirt to 4.5.0 | [CVE-2018-3639](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3639), [CVE-2018-3640](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3640) |
| AOSA-2018-0348 | Update LibRAW to 0.8.13 |
| AOSA-2018-0349 | Update LibSndFile to 1.0.28-2 | [CVE-2018-13139](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-13139), [CVE-2017-17456](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-17456), [CVE-2017-17457](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-17457) |
| AOSA-2018-0350 | Update LXC to 3.0.1-1 | [CVE-2018-6556](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6556) |
| AOSA-2018-0351 | Update Linux Kernel (Mainline) to 4.17.15 | [CVE-2018-3620](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3620), [CVE-2018-3646](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3646), [CVE-2018-3693](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3693), [CVE-2018-5390](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5390), [CVE-2018-5391](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5391), [CVE-2018-10902](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10902), [CVE-2018-13405](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-13405) |
| AOSA-2018-0352 | Update Linux Kernel (Long-Term Support) to 4.14.63 | [CVE-2018-3620](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3620), [CVE-2018-3646](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3646), [CVE-2018-3693](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3693), [CVE-2018-5390](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5390), [CVE-2018-5391](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5391), [CVE-2018-10902](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10902), [CVE-2018-13405](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-13405) |
| AOSA-2018-0353 | Update Intel Microcode to 20180807 | [CVE-2018-3620](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3620), [CVE-2018-3646](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3646), [CVE-2018-3693](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3693) |
| AOSA-2018-0354 | Update Vivaldi to 1.15.1147.64 | [CVE-2018-4117](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4117), [CVE-2018-6044](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6044), [CVE-2018-6153](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6153), [CVE-2018-6154](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6154), [CVE-2018-6155](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6155), [CVE-2018-6156](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6156), [CVE-2018-6157](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6157), [CVE-2018-6158](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6158), [CVE-2018-6159](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6159), [CVE-2018-6161](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6161), [CVE-2018-6162](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6162), [CVE-2018-6163](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6163), [CVE-2018-6164](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6164), [CVE-2018-6165](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6165), [CVE-2018-6166](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6166), [CVE-2018-6167](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6167), [CVE-2018-6168](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6168), [CVE-2018-6169](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6169), [CVE-2018-6170](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6170), [CVE-2018-6171](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6171), [CVE-2018-6172](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6172), [CVE-2018-6173](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6173), [CVE-2018-6174](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6174), [CVE-2018-6175](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6175), [CVE-2018-6176](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6176), [CVE-2018-6177](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6177), [CVE-2018-6178](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6178), [CVE-2018-6179](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6179) |
| AOSA-2018-0355 | Update WebKit2GTK+ to 2.20.5 | [CVE-2018-4246](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4246), [CVE-2018-4261](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4261), [CVE-2018-4262](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4262), [CVE-2018-4263](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4263), [CVE-2018-4264](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4264), [CVE-2018-4265](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4265), [CVE-2018-4266](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4266), [CVE-2018-4267](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4267), [CVE-2018-4270](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4270), [CVE-2018-4271](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4271), [CVE-2018-4272](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4272), [CVE-2018-4273](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4273), [CVE-2018-4278](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4278), [CVE-2018-4284](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4284), [CVE-2018-12911](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12911) |
| AOSA-2018-0356 | Update WPA Supplicant to 2.6-4 | [CVE-2018-14526](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14526) |
| AOSA-2018-0357 | Update Brave Browser to 0.23.80dev | [CVE-2018-4246](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4246), [CVE-2018-4261](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4261), [CVE-2018-4262](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4262), [CVE-2018-4263](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4263), [CVE-2018-4264](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4264), [CVE-2018-4265](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4265), [CVE-2018-4266](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4266), [CVE-2018-4267](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4267), [CVE-2018-4270](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4270), [CVE-2018-4271](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4271), [CVE-2018-4272](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4272), [CVE-2018-4273](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4273), [CVE-2018-4278](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4278), [CVE-2018-4284](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4284), [CVE-2018-12911](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12911) |
| AOSA-2018-0358 | Update PostgreSQL to 10.5 | [CVE-2018-10915](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10915), [CVE-2018-10925](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10925) |
| AOSA-2018-0359 | Update Soundtouch to 1.9.2 | [CVE-2017-9258](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-9258), [CVE-2017-9259](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-9259), [CVE-2017-9260](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-9260), [CVE-2018-14044](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14044), [CVE-2018-14045](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14045), [CVE-2018-1000223](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1000223) |
| AOSA-2018-0360 | Update GNOME Display Manager (GDM) to 3.28.2-2 | [CVE-2018-14424](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14424) |
| AOSA-2018-0361 | Update LibArchive to 3.3.2-1 | [CVE-2017-14166](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-14166), [CVE-2017-14501](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-14501), [CVE-2017-14503](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-14503) |
| AOSA-2018-0362 | Update SDDM to 0.17.0-2 | [CVE-2018-14345](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14345) |
| AOSA-2018-0363 | Update Yubico PIV Tool to 1.6.0 | [CVE-2018-14779](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14779), [CVE-2018-14780](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14780) |
| AOSA-2018-0364 | Update Samba to 4.8.4 | [CVE-2018-1139](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1139), [CVE-2018-1140](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1140), [CVE-2018-10858](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10858), [CVE-2018-10918](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10918), [CVE-2018-10919](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10919) |
| AOSA-2018-0365 | Update LibXML2 to 2.9.8-1 | [CVE-2018-9251](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-9251), [CVE-2018-14404](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14404), [CVE-2018-14567](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14567) |
| AOSA-2018-0366 | Update Flash Player PepperAPI Plugin to 30.0.0.154 | [CVE-2018-12824](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12824), [CVE-2018-12825](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12825), [CVE-2018-12826](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12826), [CVE-2018-12827](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12827), [CVE-2018-12828](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12828) |
| AOSA-2018-0367 | Update Aubio to 0.4.6-3 | [CVE-2018-14522](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14522), [CVE-2018-14523](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14523) |
| AOSA-2018-0368 | Update MariaDB to 10.3.9 | [CVE-2018-3058](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3058), [CVE-2018-3060](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3060), [CVE-2018-3063](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3063), [CVE-2018-3064](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3064), [CVE-2018-3066](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-3066) |
| AOSA-2018-0369 | Update Docker to 18.05.0-1 | [CVE-2018-10892](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10892) |
| AOSA-2018-0370 | Update SPICE to 0.14.0-2 | [CVE-2018-10873](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10873) |
| AOSA-2018-0371 | Update LibGit2 to 0.27.3 | [CVE-2018-10887](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10887), [CVE-2018-10888](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10888) |
| AOSA-2018-0372 | Update libmspack to 0.6alpha-1 | [CVE-2018-14679](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14679), [CVE-2018-14680](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14680), [CVE-2018-14681](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14681), [CVE-2018-14682](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14682) |
| AOSA-2018-0373 | Update BIND to 9.12.2.P1 | [CVE-2018-5738](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5738), [CVE-2018-5740](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-5740) |
| AOSA-2018-0374 | Update OpenSSH to 7.7p1 | [CVE-2018-15473](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-15473) |
| AOSA-2018-0375 | Update PHP to 7.2.9 | [CVE-2018-12882](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12882), [CVE-2018-12883](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12883), [CVE-2018-14851](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14851) |
| AOSA-2018-0376 | Update Node.js to 8.11.4 | [CVE-2018-12115](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-12115) |
| AOSA-2018-0377 | Update APT to 1.6.4 | [CVE-2018-0501](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-0501) |
| AOSA-2018-0378 | Update X11 Libraries to 7.7.20180825, containing LibX11 1.6.6 | [CVE-2018-14598](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14598), [CVE-2018-14599](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14599), [CVE-2018-14600](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14600) |
| AOSA-2018-0379 | Update ZUtils to 1.7-1 | [CVE-2018-1000637](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-1000637) |
| AOSA-2018-0380 | Update Pango to 1.42.4 |
| AOSA-2018-0381 | Update lrzsz to 0.12.21-2 | [CVE-2018-10195](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10195) |
| AOSA-2018-0382 | Update Confuse to 3.2.2 | [CVE-2018-14447](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-14447) |
| AOSA-2018-0383 | Update Opera to 55.0.2994.44 | [CVE-2018-4117](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-4117), [CVE-2018-6044](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6044), [CVE-2018-6153](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6153), [CVE-2018-6154](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6154), [CVE-2018-6155](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6155), [CVE-2018-6156](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6156), [CVE-2018-6157](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6157), [CVE-2018-6158](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6158), [CVE-2018-6159](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6159), [CVE-2018-6161](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6161), [CVE-2018-6162](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6162), [CVE-2018-6163](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6163), [CVE-2018-6164](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6164), [CVE-2018-6165](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6165), [CVE-2018-6166](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6166), [CVE-2018-6167](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6167), [CVE-2018-6168](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6168), [CVE-2018-6169](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6169), [CVE-2018-6170](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6170), [CVE-2018-6171](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6171), [CVE-2018-6172](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6172), [CVE-2018-6173](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6173), [CVE-2018-6174](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6174), [CVE-2018-6175](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6175), [CVE-2018-6176](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6176), [CVE-2018-6177](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6177), [CVE-2018-6178](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6178), [CVE-2018-6179](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-6179) |
| AOSA-2018-0384 | Update LibGit2 to 0.27.4 | [CVE-2018-15501](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-15501) |
