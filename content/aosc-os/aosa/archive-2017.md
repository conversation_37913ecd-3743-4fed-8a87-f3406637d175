+++
title = "List of Announced AOSAs (2017)"
description = "Archive of Announced AOSC OS Security Advisories (2017)"
date = 2020-05-04T03:34:50.287Z
[taxonomies]
tags = ["aosa"]
[extra]
page_hack = "big-min-table-cell-width"
+++

| <center>AOSA</center> |  <center>Suggestion(s)</center> | <center>Fixed CVE(s)</center> |
| ------------ | --------------- | ------------------ |
| AOSA-2017-0093 | Update LibIDN2 to 2.0.4 | CVE-2017-14062 |
| AOSA-2017-0094 | Update GNU PSPP to 1.0.1 | CVE-2017-12958, CVE-2017-12959, CVE-2017-12960, CVE-2017-12961 |
| AOSA-2017-0095 | Update CVS to 1.12.13-1 | CVE-2017-12836 |
| AOSA-2017-0096 | Update Exim to 4.89-1 | CVE-2017-1000369, CVE-2017-1000376 |
| AOSA-2017-0097 | Update Emacs to 25.3 | CVE-2017-14482 |
| AOSA-2017-0098 | Update cURL to 7.55.0-1 | CVE-2017-1000254 |
| AOSA-2017-0099 | Update Varnish to 5.1.2-1 | CVE-2017-12425 |
| AOSA-2017-0100 | Update Apache/HTTPD to 2.4.28 | CVE-2017-3167, CVE-2017-3169, CVE-2017-7659, CVE-2017-7668, CVE-2017-7679, CVE-2017-9788, CVE-2017-9789, CVE-2017-9798 |
| AOSA-2017-0101 | Update Samba to 4.6.8 | CVE-2017-12150, CVE-2017-12151, CVE-2017-12163 |
| AOSA-2017-0102 | Update LibRAW to 0.18.5 | CVE-2017-13735, CVE-2017-14265, CVE-2017-14348 |
| AOSA-2017-0103 | Update LibZip to 1.2.0-1 | CVE-2017-14107 |
| AOSA-2017-0104 | Update ImageMagick to 6.9.8+10-1 | CVE-2017-14741 |
| AOSA-2017-0105 | Update LibGD to 2.2.5 | CVE-2017-6362, CVE-2017-7890 |
| AOSA-2017-0106 | Update WeeChat to 1.9.1 | CVE-2017-14727 |
| AOSA-2017-0107 | Update WebKit2GTK (or webkitgtk-4.0) to 2.16.6 | CVE-2017-7018, CVE-2017-7030, CVE-2017-7034, CVE-2017-7037, CVE-2017-7039, CVE-2017-7046, CVE-2017-7048, CVE-2017-7055, CVE-2017-7056, CVE-2017-7061, CVE-2017-7064 |
| AOSA-2017-0108 | Update Node.js to 8.1.4 | CVE-2017-11499 |
| AOSA-2017-0109 | Update PostgreSQL to 9.6.5 | CVE-2017-7546, CVE-2017-7547, CVE-2017-7548 |
| AOSA-2017-0110 | Update LibSampleRate to 0.1.9 | CVE-2017-7697 |
| AOSA-2017-0111 | Update X.Org Server (xorg-server) to 1.9.13-2 |CVE-2017-13721, CVE-2017-13723 |
| AOSA-2017-0112 | Update WavPack to 5.1.0 | CVE-2016-10169, CVE-2016-10170, CVE-2016-10171, CVE-2016-10172 |
| AOSA-2017-0113 | Update mbed TLS (mbedtls) to 2.6.0 | CVE-2017-14032 |
| AOSA-2017-0114 | Update Qemu to 2.9.0-3 | CVE-2017-12809, CVE-2017-13672, CVE-2017-13711, CVE-2017-14167 |
| AOSA-2017-0115 | Update Chromium and Google Chrome to 61.0.3163.100 | CVE-2017-5091, CVE-2017-5092, CVE-2017-5093, CVE-2017-5094, CVE-2017-5095, CVE-2017-5096, CVE-2017-5097, CVE-2017-5098, CVE-2017-5099, CVE-2017-5100, CVE-2017-5101, CVE-2017-5102, CVE-2017-5103, CVE-2017-5104, CVE-2017-5105, CVE-2017-5106, CVE-2017-5107, CVE-2017-5108, CVE-2017-5109, CVE-2017-5110, CVE-2017-5111, CVE-2017-5112, CVE-2017-5113, CVE-2017-5114, CVE-2017-5115, CVE-2017-5116, CVE-2017-5117, CVE-2017-5118, CVE-2017-5119, CVE-2017-5120, CVE-2017-5121, CVE-2017-5122, CVE-2017-6991 |
| AOSA-2017-0116 | Update OpenJDK to 8u144b01 | CVE-2017-3509, CVE-2017-3511, CVE-2017-3512, CVE-2017-3514, CVE-2017-3526, CVE-2017-3533, CVE-2017-3539, CVE-2017-3544, CVE-2017-10053, CVE-2017-10067, CVE-2017-10074, CVE-2017-10078, CVE-2017-10081, CVE-2017-10086, CVE-2017-10087, CVE-2017-10089, CVE-2017-10090, CVE-2017-10096, CVE-2017-10101, CVE-2017-10102, CVE-2017-10104, CVE-2017-10105, CVE-2017-10107, CVE-2017-10108, CVE-2017-10109, CVE-2017-10110, CVE-2017-10111, CVE-2017-10114, CVE-2017-10115, CVE-2017-10116, CVE-2017-10117, CVE-2017-10118, CVE-2017-10121, CVE-2017-10125, CVE-2017-10135, CVE-2017-10145, CVE-2017-10176, CVE-2017-10193, CVE-2017-10198, CVE-2017-10243 |
| AOSA-2017-0117 | Update Go to 1.9.1 | CVE-2017-15041, CVE-2017-15042 |
| AOSA-2017-0118 | Update FFmpeg to 3.3.4 | CVE-2017-9608, CVE-2017-11399, CVE-2017-11665, CVE-2017-11719, CVE-2017-14054, CVE-2017-14055, CVE-2017-14056, CVE-2017-14057, CVE-2017-14058, CVE-2017-14059, CVE-2017-14169, CVE-2017-14170, CVE-2017-14171, CVE-2017-14222, CVE-2017-14223, CVE-2017-14225, CVE-2017-14767 |
| AOSA-2017-0119 | Update FreeImage to 3.17.0-1 | CVE-2015-0852, CVE-2016-5684 |
| AOSA-2017-0120 | Update PHP to 7.1.10 | CVE-2017-7890, CVE-2017-9224, CVE-2017-9226, CVE-2017-9227, CVE-2017-9228, CVE-2017-9229 |
| AOSA-2017-0121 | Update Vorbis-Tools to 1.4.0-3 | CVE-2014-9638, CVE-2014-9639, CVE-2015-6749 |
| AOSA-2017-0122 | Update OpenJPEG to 2.2.0-1 | CVE-2017-14152 |
| AOSA-2017-0123 | Update FontForge to 20161012-1 | CVE-2010-4259, CVE-2017-11568, CVE-2017-11569, CVE-2017-11571, CVE-2017-11572, CVE-2017-11574, CVE-2017-11575, CVE-2017-11576, CVE-2017-11577 |
| AOSA-2017-0124 | Update Connman to 1.33-2 | CVE-2017-12865 |
| AOSA-2017-0125 | Update LibXML2 to 2.9.5 | CVE-2017-0663, CVE-2017-7375, CVE-2017-7376, CVE-2017-9047 CVE-2017-9048, CVE-2017-9049, CVE-2017-9050 |
| AOSA-2017-0126 | Update Gajim to 0.16.8 | CVE-2016-10376 |
| AOSA-2017-0127 | Update Augeas to 1.4.0-1 | CVE-2017-7555 |
| AOSA-2017-0128 | Update CatDoc to 0.94.4-2 | CVE-2017-11110 |
| AOSA-2017-0129 | Update Expat to 2.2.4 | CVE-2012-0876, CVE-2016-0718, CVE-2016-5300, CVE-2016-9063, CVE-2017-9233, CVE-2017-11742 |
| AOSA-2017-0130 | Update LibTIFF to 4.0.8-1 | CVE-2016-10095, CVE-2017-9147, CVE-2017-9403, CVE-2017-9404, CVE-2017-9936, CVE-2017-10688 |
| AOSA-2017-0131 | Update Firefox to 56.0 | CVE-2017-7793, CVE-2017-7805, CVE-2017-7810, CVE-2017-7811, CVE-2017-7812, CVE-2017-7813, CVE-2017-7814, CVE-2017-7815, CVE-2017-7816, CVE-2017-7817, CVE-2017-7818, CVE-2017-7819, CVE-2017-7820, CVE-2017-7821, CVE-2017-7822, CVE-2017-7823, CVE-2017-7824, CVE-2017-7825 |
| AOSA-2017-0132 | Update LibTIRPC to 0.3.2-2 | CVE-2017-8779 |
| AOSA-2017-0133 | Update LibMWAW to 0.3.11-1 | CVE-2017-9433 |
| AOSA-2017-0134 | Update GhostScript to 9.20-2 | CVE-2017-8291 |
| AOSA-2017-0135 | Update LibEvent to 2.0.22-1 | CVE-2016-10195, CVE-2016-10196, CVE-2016-10197 |
| AOSA-2017-0136 | Update TeXLive to 20150521-6 | CVE-2016-10243 |
| AOSA-2017-0137 | Update Opera to 48.0.2685.35 | CVE-2017-5091, CVE-2017-5092, CVE-2017-5093, CVE-2017-5094, CVE-2017-5095, CVE-2017-5096, CVE-2017-5097, CVE-2017-5098, CVE-2017-5099, CVE-2017-5100, CVE-2017-5101, CVE-2017-5102, CVE-2017-5103, CVE-2017-5104, CVE-2017-5105, CVE-2017-5106, CVE-2017-5107, CVE-2017-5108, CVE-2017-5109, CVE-2017-5110, CVE-2017-5111, CVE-2017-5112, CVE-2017-5113, CVE-2017-5114, CVE-2017-5115, CVE-2017-5116, CVE-2017-5117, CVE-2017-5118, CVE-2017-5119, CVE-2017-5120, CVE-2017-5121, CVE-2017-5122, CVE-2017-6991 |
| AOSA-2017-0138 | Update Minicom to 2.7.1 | CVE-2017-7467 |
| AOSA-2017-0139 | Update Wget to 1.19.1 | CVE-2017-6508 |
| AOSA-2017-0140 | Update Nemo to 3.4.5-1 | CVE-2017-14604 |
| AOSA-2017-0141 | Update LibFM to 1.2.5-2 | CVE-2017-14604 |
| AOSA-2017-0142 | Update LibFM-Qt to 0.11.2-3 | CVE-2017-14604 |
| AOSA-2017-0143 | Update Vivaldi to 1.12.955.38 | CVE-2017-5091, CVE-2017-5092, CVE-2017-5093, CVE-2017-5094, CVE-2017-5095, CVE-2017-5096, CVE-2017-5097, CVE-2017-5098, CVE-2017-5099, CVE-2017-5100, CVE-2017-5101, CVE-2017-5102, CVE-2017-5103, CVE-2017-5104, CVE-2017-5105, CVE-2017-5106, CVE-2017-5107, CVE-2017-5108, CVE-2017-5109, CVE-2017-5110, CVE-2017-5111, CVE-2017-5112, CVE-2017-5113, CVE-2017-5114, CVE-2017-5115, CVE-2017-5116, CVE-2017-5117, CVE-2017-5118, CVE-2017-5119, CVE-2017-5120, CVE-2017-5121, CVE-2017-5122, CVE-2017-6991 |
| AOSA-2017-0144 | Update LibZip to 1.2.0-2 | CVE-2017-12858 |
| AOSA-2017-0145 | Update PCRE to 8.41, and PCRE2 to 10.30 | CVE-2017-7186, CVE-2017-8399, CVE-2017-8786 |
| AOSA-2017-0146 | Update Krb5 to 1.15.2 | CVE-2017-11368, CVE-2017-11462 |
| AOSA-2017-0147 | Update Pale Moon to 27.5.0 | CVE-2017-7751, CVE-2017-7757, CVE-2017-7763, CVE-2017-7781, CVE-2017-7783, CVE-2017-7784, CVE-2017-7787, CVE-2017-7800, CVE-2017-7804, CVE-2017-7809 |
| AOSA-2017-0148 | Update LibWPD to 0.10.2 | CVE-2017-14226 |
| AOSA-2017-0149 | Update GhostScript to 9.20-3 | CVE-2016-7976, CVE-2016-7977, CVE-2016-7978, CVE-2016-7979, CVE-2016-8602, CVE-2016-9601, CVE-2016-10217, CVE-2016-10218, CVE-2016-10219, CVE-2016-10220, CVE-2017-5951, CVE-2017-7207, CVE-2017-8291, CVE-2017-9611, CVE-2017-9612, CVE-2017-9726, CVE-2017-9727, CVE-2017-9739, CVE-2017-9835, CVE-2017-11714 |
| AOSA-2017-0151 | Update Brave Browser to 0.18.36 | CVE-2017-5091, CVE-2017-5092, CVE-2017-5093, CVE-2017-5094, CVE-2017-5095, CVE-2017-5096, CVE-2017-5097, CVE-2017-5098, CVE-2017-5099, CVE-2017-5100, CVE-2017-5101, CVE-2017-5102, CVE-2017-5103, CVE-2017-5104, CVE-2017-5105, CVE-2017-5106, CVE-2017-5107, CVE-2017-5108, CVE-2017-5109, CVE-2017-5110, CVE-2017-5111, CVE-2017-5112, CVE-2017-5113, CVE-2017-5114, CVE-2017-5115, CVE-2017-5116, CVE-2017-5117, CVE-2017-5118, CVE-2017-5119, CVE-2017-5120, CVE-2017-5121, CVE-2017-5122, CVE-2017-6991 |
| AOSA-2017-0152 | Update OCaml to 4.04.2 | CVE-2017-9772 |
| AOSA-2017-0154 | Update ImageMagick to 6.9.8+10-2 | CVE-2017-0903 |
| AOSA-2017-0155 | Update X11 Libraries to 7.7.20171011 | CVE-2017-13720, CVE-2017-13722 |
| AOSA-2017-0156 | Update LAME to 3.99.5-4 | CVE-2017-15018 |
| AOSA-2017-0157 | Update Thunderbird to 52.4.0 | CVE-2017-7793, CVE-2017-7805, CVE-2017-7810, CVE-2017-7814, CVE-2017-7818, CVE-2017-7819, CVE-2017-7823, CVE-2017-7824, CVE-2017-7825 |
| AOSA-2017-0158 | Update SeaMonkey to 2.48.0 | CVE-2017-5397 |
| AOSA-2017-0159 | Update Open vSwitch to 2.7.0-1 | CVE-2017-9214, CVE-2017-9263, CVE-2017-9264, CVE-2017-9265 |
| AOSA-2017-0160 | Update NSS to 3.31-1 | CVE-2017-7805 |
| AOSA-2017-0161 | Update Wireshark to 2.2.10 | CVE-2017-15192, CVE-2017-15193, CVE-2017-15191 |
| AOSA-2017-0162 | Update Pale Moon to 27.5.1 |
| AOSA-2017-0163 | Update Libtasn1 to 4.12 | CVE-2017-10790 |
| AOSA-2017-0164 | Update Shadowsocks-Libev to 3.0.8-1 | CVE-2017-15924 |
| AOSA-2017-0165 | Update Xorg-Sever to 1.19.3-2 | CVE-2017-12176, CVE-2017-12177, CVE-2017-121778, CVE-2017-121779, CVE-2017-121780, CVE-2017-121781, CVE-2017-121782, CVE-2017-121783, CVE-2017-121784, CVE-2017-121785, CVE-2017-121786, CVE-2017-121787 |
| AOSA-2017-0166 | Update Linux Kernel to 4.13.3-2, and 4.9.51-1 (LTS) | CVE-2017-5123, CVE-2017-12192, CVE-2017-14991, CVE-2017-15265 |
| AOSA-2017-0167 | Update WPA_Supplicant to 2.6-2 | CVE-2017-13077, CVE-2017-13078, CVE-2017-13079, CVE-2017-13080, CVE-2017-13081, CVE-2017-13082, CVE-2017-13086, CVE-2017-13087, CVE-2017-13088 |
| AOSA-2017-0168 | Update Flashplayer-PPAPI to 27.0.0.170 | CVE-2017-11292 |
| AOSA-2017-0169 | Update MuPDF to 1.11-2 | CVE-2017-15587 |
| AOSA-2017-0170 | Update Chromium and Google Chrome to 62.0.3202.62 | CVE-2017-5124, CVE-2017-5125, CVE-2017-5126, CVE-2017-5127, CVE-2017-5128, CVE-2017-5129, CVE-2017-5130,CVE-2017-5131, CVE-2017-5132, CVE-2017-5133, CVE-2017-15386, CVE-2017-15387, CVE-2017-15388, CVE-2017-15389, CVE-2017-15390, CVE-2017-15391, CVE-2017-15392, CVE-2017-15393, CVE-2017-15394, CVE-2017-15395 |
| AOSA-2017-0171 | Update Brave Browser to 0.19.48 | CVE-2017-5124, CVE-2017-5125, CVE-2017-5126, CVE-2017-5127, CVE-2017-5128, CVE-2017-5129, CVE-2017-5130,CVE-2017-5131, CVE-2017-5132, CVE-2017-5133, CVE-2017-15386, CVE-2017-15387, CVE-2017-15388, CVE-2017-15389, CVE-2017-15390, CVE-2017-15391, CVE-2017-15392, CVE-2017-15393, CVE-2017-15394, CVE-2017-15395 |
| AOSA-2017-0172 | Update HostAPd to 2.6-1 | CVE-2017-13077, CVE-2017-13078, CVE-2017-13079, CVE-2017-13080, CVE-2017-13081, CVE-2017-13082, CVE-2017-13086, CVE-2017-13087, CVE-2017-13088 |
| AOSA-2017-0173 | Update LibVirt to 3.1.0-2 | CVE-2017-1000256 |
| AOSA-2017-0174 | Update Postfix to 3.2.2 |
| AOSA-2017-0175 | Update LAME to 3.100 | CVE-2017-9410, CVE-2017-9411, CVE-2017-9412 |
| AOSA-2017-0176 | Update Irssi to 1.0.5 | CVE-2017-15228, CVE-2017-15227, CVE-2017-15721, CVE-2017-15723, CVE-2017-15722 |
| AOSA-2017-0177 | Update cURL to 7.55.0-2 | CVE-2017-1000257 |
| AOSA-2017-0178 | Update OpenJDK to 8u152b16 |
| AOSA-2017-0179 | Update Bzr (Bazaar) to 2.7.0-1 | CVE-2017-14176 |
| AOSA-2017-0180 | Update ICU to 58.2-5 | CVE-2017-14952 |
| AOSA-2017-0181 | Update MuPDF to 1.11-3 | CVE-2017-14685, CVE-2017-14686, CVE-2017-14687 |
| AOSA-2017-0182 | Update LibIDN to 1.33-2 | CVE-2017-14062 |
| AOSA-2017-0183 | Update Node.js to 8.1.4-1 | CVE-2017-14919 |
| AOSA-2017-0184 | Update Poppler to 0.60.1 | CVE-2017-9775, CVE-2017-9865 |
| AOSA-2017-0185 | Update LibLouis to 3.3.0 | CVE-2017-13738, CVE-2017-13739, CVE-2017-13740, CVE-2017-13741, CVE-2017-13742, CVE-2017-13743, CVE-2017-13744 |
| AOSA-2017-0186 | Update HHVM to 3.22.0 |
| AOSA-2017-0187 | Update Qt 5 (qt-5) to 5.9.2+wk5.212.0 | CVE-2017-5092, CVE-2017-5093, CVE-2017-5095, CVE-2017-5097, CVE-2017-5099, CVE-2017-5102, CVE-2017-5103, CVE-2017-5107, CVE-2017-5112, CVE-2017-5114, CVE-2017-5117, CVE-2017-5118 |
| AOSA-2017-0188 | Update Kodi to 17.4 |
| AOSA-2017-0189 | Update Wget to 1.19.1-2 | CVE-2017-13089, CVE-2017-13090 |
| AOSA-2017-0190 | Update VirtualBox to 5.1.30 |
| AOSA-2017-0191 | Update Apache Portable Runtime to 1.6.3 | CVE-2017-12613 |
| AOSA-2017-0192 | Update APR-Util to 1.6.1 | CVE-2017-12618 |
| AOSA-2017-0193 | Update Chromium and Google Chrome to 62.0.3202.75 | CVE-2017-15396 |
| AOSA-2017-0194 | Update Brave Browser to 0.19.70 | CVE-2017-15396 |
| AOSA-2017-0195 | Update Vivaldi to 1.12.955.42 | CVE-2017-15396 |
| AOSA-2017-0196 | Update WebKit2GTK to 2.18.0 | CVE-2017-7081, CVE-2017-7087, CVE-2017-7089, CVE-2017-7090, CVE-2017-7091, CVE-2017-7092, CVE-2017-7093, CVE-2017-7094, CVE-2017-7095, CVE-2017-7096, CVE-2017-7098, CVE-2017-7099, CVE-2017-7100, CVE-2017-7102, CVE-2017-7104, CVE-2017-7107, CVE-2017-7109, CVE-2017-7111, CVE-2017-7117, CVE-2017-7120, CVE-2017-7142 |
| AOSA-2017-0197 | Update RPM to 4.12.0.2 | CVE-2013-6435, CVE-2014-8118 |
| AOSA-2017-0198 | Update OpenJPEG to 2.2.0-2 | CVE-2017-14039, CVE-2017-14041, CVE-2017-14040,  CVE-2017-14151 |
| AOSA-2017-0199 | Update OpenSSL to 1.0.2m | CVE-2017-3735, CVE-2017-3736 |
| AOSA-2017-0200 | Update Chromium and Google Chrome to 62.0.3202.89 | CVE-2017-15398, CVE-2017-15399 |
| AOSA-2017-0201 | Update Brave Browser to 0.19.80 | CVE-2017-15396 |
| AOSA-2017-0202 | Update PostgreSQL to 10.0-1 | CVE-2017-12172, CVE-2017-15098, CVE-2017-15099 |
| AOSA-2017-0203 | Update MariaDB to 10.2.10 | CVE-2017-10378, CVE-2017-10268 |
| AOSA-2017-0204 | Update LibJPEG-Turbo to 1.5.2-1 | CVE-2017-15232 |
| AOSA-2017-0205 | Update Brave Browser to 0.19.88 | CVE-2017-15398, CVE-2017-15399 |
| AOSA-2017-0206 | Update WebKit2GTK+ to 2.18.3 | CVE-2017-13783, CVE-2017-13784, CVE-2017-13785, CVE-2017-13788, CVE-2017-13791, CVE-2017-13792, CVE-2017-13793, CVE-2017-13794, CVE-2017-13795, CVE-2017-13796, CVE-2017-13798, CVE-2017-13802, CVE-2017-13803 |
| AOSA-2017-0207 | Update Konversation to 1.6.2-3 | CVE-2017-15923 |
| AOSA-2017-0208 | Update Firefox to 57.0 | CVE-2017-7826, CVE-2017-7827, CVE-2017-7828, CVE-2017-7830, CVE-2017-7831, CVE-2017-7832, CVE-2017-7833, CVE-2017-7834, CVE-2017-7835, CVE-2017-7836, CVE-2017-7837, CVE-2017-7838, CVE-2017-7839, CVE-2017-7840, CVE-2017-7842 |
| AOSA-2017-0209 | Update CouchDB to 2.1.1 | CVE-2017-12635, CVE-2017-12636 |
| AOSA-2017-0210 | Update FlashPlayer-PPAPI (Pepper API) to 27.0.0.187. | CVE-2017-3112, CVE-2017-3114, CVE-2017-11213, CVE-2017-11215, CVE-2017-11225 |
| AOSA-2017-0211 | Update Varnish to 5.1.2-2 | CVE-2017-8807 |
| AOSA-2017-0212 | Update PHP 7 to 7.1.11 | CVE-2016-1283 |
| AOSA-2017-0213 | Update jq to 1.5-1 | CVE-2015-8863, CVE-2016-4074 |
| AOSA-2017-0214 | Update Opera to 49.0.2725.39 | CVE-2017-5124, CVE-2017-5125, CVE-2017-5126, CVE-2017-5127, CVE-2017-5128, CVE-2017-5129, CVE-2017-5130,CVE-2017-5131, CVE-2017-5132, CVE-2017-5133, CVE-2017-15386, CVE-2017-15387, CVE-2017-15388, CVE-2017-15389, CVE-2017-15390, CVE-2017-15391, CVE-2017-15392, CVE-2017-15393, CVE-2017-15394, CVE-2017-15395, CVE-2017-15396, CVE-2017-15398, CVE-2017-15399 |
| AOSA-2017-0215 | Update DotNet-Runtime and DotNet-SDK to 2.0.3 | CVE-2017-11770 |
| AOSA-2017-0216 | Update Samba to 4.7.0-1 | CVE-2017-14746, CVE-2017-15275 |
| AOSA-2017-0217 | Update LDNS to 1.7.0-2 | CVE-2017-1000231, CVE-2017-1000232 |
| AOSA-2017-0218 | Update VLC to 2.2.6-3 | CVE-2017-9300, CVE-2017-10699 |
| AOSA-2017-0219 | Update Perl XML::LibXML to 2.0128-1 | CVE-2017-10672 |
| AOSA-2017-0220 | Update Telegram Desktop to 1.1.23-1 | CVE-2016-10351 |
| AOSA-2017-0221 | Update Exim to 4.89.1 | CVE-2017-16943, CVE-2017-16944 |
| AOSA-2017-0222 | Update OptiPNG to 0.7.6-2 | CVE-2017-1000229 |
| AOSA-2017-0223 | Update X11 Libraries Meta to 7.7.20171201 | CVE-2017-16611, CVE-2017-16612 |
| AOSA-2017-0224 | Update cURL to 7.57.0 | CVE-2017-8816, CVE-2017-8817, CVE-2017-8818 |
| AOSA-2017-0225 | Update Thunderbird to 52.5.0 | CVE-2017-7826, CVE-2017-7828, CVE-2017-7830 |
| AOSA-2017-0226 | Update Firefox to 57.0.1 | CVE-2017-7843, CVE-2017-7844 |
| AOSA-2017-0227 | Update Qemu to 2.9.1-2 | CVE-2017-17381 |
| AOSA-2017-0228 | Update Tor to ******* | CVE-2017-8819, CVE-2017-8820, CVE-2017-8821, CVE-2017-8822, CVE-2017-8823 |
| AOSA-2017-0229 | Revoked |
| AOSA-2017-0230 | Update OpenSSL to 1.0.2n | CVE-2017-3737, CVE-2017-3738 |
| AOSA-2017-0231 | Update Rsync to 3.1.2-2 | CVE-2017-17433, CVE-2017-17434 |
| AOSA-2017-0232 | Update Wireshark to 2.4.3 |
| AOSA-2017-0233 | Update Chromium and Google Chrome to 63.0.3239.84 | CVE-2017-15407, CVE-2017-15408, CVE-2017-15409, CVE-2017-15410, CVE-2017-15411, CVE-2017-15412, CVE-2017-15413, CVE-2017-15415, CVE-2017-15416, CVE-2017-15417, CVE-2017-15418, CVE-2017-15419, CVE-2017-15420, CVE-2017-15422, CVE-2017-15423, CVE-2017-15424, CVE-2017-15425, CVE-2017-15426, CVE-2017-15427 |
| AOSA-2017-0234 | Update Erlang to 20.1.7 | CVE-2017-1000385 |
| AOSA-2017-0235 | Update Node.js to 8.9.3 | CVE-2017-3738, CVE-2017-15896, CVE-2017-15897 |
| AOSA-2017-0236 | Update OptiPNG to 0.7.6-3 | CVE-2017-16938 |
| AOSA-2017-0237 | Update Mercurial to 4.4.1 | CVE-2017-17458 |
| AOSA-2017-0238 | Update GraphicsMagick to 1.3.27 | CVE-2017-11102 |
| AOSA-2017-0239 | Update FFmpeg to 3.4.1 | CVE-2017-16840, CVE-2017-17081 |
| AOSA-2017-0240 | Update LibXML2 to 2.9.5-1 | CVE-2017-15412 |
| AOSA-2017-0241 | Update Chromium and Google Chrome to 63.0.3239.108 | CVE-2017-15429 |
| AOSA-2017-0242 | Update FontForge to 20170731 | CVE-2017-11570, CVE-2017-11573 |
| AOSA-2017-0243 | Update Lynx to 2.8.9dev16 | CVE-2017-1000211 |
| AOSA-2017-0244 | Update Rsync to 3.1.2-3 | CVE-2017-16548 |
| AOSA-2017-0245 | Update Linux Kernel (mainline) to 4.13.8 | CVE-2017-12190 |
| AOSA-2017-0246 | Update OpenCV to 3.3.1 | CVE-2017-12597, CVE-2017-12598, CVE-2017-12599, CVE-2017-12600, CVE-2017-12601, CVE-2017-12602, CVE-2017-12603, CVE-2017-12604, CVE-2017-12605, CVE-2017-12606, CVE-2017-12862, CVE-2017-12863, CVE-2017-12864, CVE-2017-14136 |
| AOSA-2017-0247 | Update Flash Player PepperAPI Plugin to 28.0.0.126 | CVE-2017-11305 |
| AOSA-2017-0248 | Update Vivaldi Browser to 1.13.1008.36 |
| AOSA-2017-0249 | Update WebKit2GTK+ to 2.18.4 | CVE-2017-7156, CVE-2017-7157, CVE-2017-13856, CVE-2017-13866, CVE-2017-13870 |
| AOSA-2017-0250 | Update Ruby to 2.4.3 | CVE-2017-17405 |
| AOSA-2017-0251 | Update GIMP to 2.8.22-1 | CVE-2017-17784, CVE-2017-17785, CVE-2017-17786, CVE-2017-17787, CVE-2017-17788, CVE-2017-17789 |
| AOSA-2017-0252 | Update XRDP to 0.9.4-1 | CVE-2017-16927 |
| AOSA-2017-0253 | Update ImageMagick to 6.9.9+27 | CVE-2017-11188, CVE-2017-11478, CVE-2017-11523, CVE-2017-11527, CVE-2017-11535, CVE-2017-11640, CVE-2017-11752, CVE-2017-12140, CVE-2017-12435, CVE-2017-12587, CVE-2017-12644, CVE-2017-12662, CVE-2017-12669, CVE-2017-12983, CVE-2017-13134, CVE-2017-13769, CVE-2017-14138, CVE-2017-14172, CVE-2017-14173, CVE-2017-14175, CVE-2017-14341, CVE-2017-14342, CVE-2017-14531, CVE-2017-14607, CVE-2017-14682, CVE-2017-14733, CVE-2017-14989, CVE-2017-15217, CVE-2017-15930, CVE-2017-16545, CVE-2017-16546, CVE-2017-16669 |
| AOSA-2017-0254 | Update LibRAW to 0.18.6 | CVE-2017-16909, CVE-2017-16910 |
| AOSA-2017-0255 | Update Transfig to 3.2.5e-4 | CVE-2017-16899 |
| AOSA-2017-0256 | Update Thunderbird to 52.5.2 | CVE-2017-7829, CVE-2017-7845, CVE-2017-7846, CVE-2017-7847, CVE-2017-7848 |
| AOSA-2017-0257 | Update Linux Firmware (Free and Non-Free) to 20171125 | CVE-2016-0801, CVE-2017-0561, CVE-2017-9417, CVE-2017-13080, CVE-2017-13081 |
