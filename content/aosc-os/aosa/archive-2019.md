+++
title = "List of Announced AOSAs (2019)"
description = "Archive of Announced AOSC OS Security Advisories (2019)"
date = 2020-05-04T03:34:57.724Z
[taxonomies]
tags = ["aosa"]
[extra]
page_hack = "big-min-table-cell-width"
+++

# Security updates

| AOSA | Package | Branch | Vendor Advisory |
| --- | --- | --- | --- |
| AOSA-2019-0001 | [`flashplayer-ppapi`: update to **********](https://github.com/AOSC-Dev/aosc-os-abbs/issues/1529) | [`stable`](https://github.com/AOSC-Dev/aosc-os-abbs/commit/95ded0e87c7921c736083681be91b4b785356617) | [Adobe Security Bulletin APSB18-42](https://helpx.adobe.com/security/products/flash-player/apsb18-42.html) |
| AOSA-2019-0002 | [`vivaldi`: update to 2.2.1388.37](https://github.com/AOSC-Dev/aosc-os-abbs/issues/1539) | [`stable`](https://github.com/AOSC-Dev/aosc-os-abbs/commit/65a2185e775d3ea7747067ba6b48157fcde993b3) | (Chrome) [Stable Channel Update for Desktop Tuesday, December 4, 2018](https://chromereleases.googleblog.com/2018/12/stable-channel-update-for-desktop.html), [Stable Channel Update for Desktop Wednesday, December 12, 2018](https://chromereleases.googleblog.com/2018/12/stable-channel-update-for-desktop_12.html) |
| AOSA-2019-0003 | [`opera`: update to 57.0.3098.106](https://github.com/AOSC-Dev/aosc-os-abbs/issues/1565) | [`stable`](https://github.com/AOSC-Dev/aosc-os-abbs/commit/c610f4aefaa1a82167568d49ccd905ded2aebcb2) | [Opera 57.0.3098.106 Stable update](https://blogs.opera.com/desktop/2018/12/opera-57-0-3098-106-stable-update/) |
| AOSA-2019-0004 | [`django`: update to 2.1.5](https://github.com/AOSC-Dev/aosc-os-abbs/issues/1568) | [`stable`](https://github.com/AOSC-Dev/aosc-os-abbs/commit/7ea0a9a4ffa88914e82465e4e2d348026ceedfb4) | [Django security releases issued: 2.1.5, 2.0.10, and 1.11.18](https://www.djangoproject.com/weblog/2019/jan/04/security-releases/) |
| AOSA-2019-0005 | [`dotnet-{runtime,sdk}`: update to 2.1.7, 2.1.503](https://github.com/AOSC-Dev/aosc-os-abbs/issues/1588) | [`stable, runtime`](https://github.com/AOSC-Dev/aosc-os-abbs/commit/fec5605893dfe8310f6dc548c19419d3e85ff761) [`stable, sdk`](https://github.com/AOSC-Dev/aosc-os-abbs/commit/9be452a81affb26d5002b8f1f4a6fc1ba77b036b) | [.NET Core 2.1.7 Update - January 08, 2019](https://github.com/dotnet/core/blob/master/release-notes/2.1/2.1.7/2.1.7.md) |
| AOSA-2019-0006 | [`firefox`: update to 64.0.2](https://github.com/AOSC-Dev/aosc-os-abbs/issues/1536) | [`stable`](https://github.com/AOSC-Dev/aosc-os-abbs/commit/19c2601c44671478f2f253828ed8c5bb366f23e8) | [Mozilla Foundation Security Advisory 2018-29](https://www.mozilla.org/en-US/security/advisories/mfsa2018-29/) |
| AOSA-2019-0007 | [`go`: update to 1.11.4](https://github.com/AOSC-Dev/aosc-os-abbs/issues/1537) | [`stable`](https://github.com/AOSC-Dev/aosc-os-abbs/commit/0cbe8cfd48fbb510060502c61517af6c97b6af24) | [\[security\] Go 1.11.3 and Go 1.10.6 are released](https://groups.google.com/forum/#!topic/golang-announce/Kw31K8G7Fi0) |
| AOSA-2019-0008 | [`tcpdump`: patch](https://github.com/AOSC-Dev/aosc-os-abbs/issues/1547) | [`stable`](https://github.com/AOSC-Dev/aosc-os-abbs/commit/fd35c4a096733f4c8a4c9f7e1951db77b9cb1766) | [The Problem](https://github.com/zyingp/temp/blob/master/tcpdump.md) |
| AOSA-2019-0009 | [`systemd`: patch](https://github.com/AOSC-Dev/aosc-os-abbs/issues/1559) | [`stable`](https://github.com/AOSC-Dev/aosc-os-abbs/commit/f51ebd49f6898eeea81327f7a70b201389c8429f) | [tmpfiles: symlinks are followed in non-terminal path components (CVE-2018-6954)](https://github.com/systemd/systemd/issues/7986) |
| AOSA-2019-0010 | [`nettle`: update to 3.4.1](https://github.com/AOSC-Dev/aosc-os-abbs/issues/1560) | [`stable`](https://github.com/AOSC-Dev/aosc-os-abbs/commit/5fc42739bea01fb1e2fe0d7ae42a158aefd3a8a6) | [The 9 Lives of Bleichenbacher's CAT: New Cache ATtacks on TLS Implementations](https://cat.eyalro.net/) |
| AOSA-2019-0011 | [`wget`: patch](https://github.com/AOSC-Dev/aosc-os-abbs/issues/1566) | [`stable`](https://github.com/AOSC-Dev/aosc-os-abbs/commit/dc729286106b2fdb44a87bbb03dd2270f845e7cd) | [\[Bug-wget\] CVE-2018-20483 counter-measure](https://lists.gnu.org/archive/html/bug-wget/2018-12/msg00034.html) |
| AOSA-2019-0012 | [`aria2`: patch](https://github.com/AOSC-Dev/aosc-os-abbs/issues/1569) | [`stable`](https://github.com/AOSC-Dev/aosc-os-abbs/commit/6dc6f2a8c87a3e07ceaa6615b72b206a51be1c48) | [Metadata and potential password leaks via --log=](https://github.com/aria2/aria2/issues/1329) |
| AOSA-2019-0013 | [`uriparser`: update to 0.9.1](https://github.com/AOSC-Dev/aosc-os-abbs/issues/1570) | [`stable`](https://github.com/AOSC-Dev/aosc-os-abbs/commit/22d7a91827c28597ace12a3009c2633044d8bcfb) | [ChangeLog](https://github.com/uriparser/uriparser/blob/uriparser-0.9.1/ChangeLog) |
| AOSA-2019-0014 | [`units`: update to 2.18](https://github.com/AOSC-Dev/aosc-os-abbs/issues/1572) | [`stable`](https://github.com/AOSC-Dev/aosc-os-abbs/commit/44eb52cc54750b3de3522145206703a4e70b8057) | [units_cur: missing input validation](https://bugs.debian.org/cgi-bin/bugreport.cgi?bug=902935) |
| AOSA-2019-0015 | [`imagemagick`: update to 6.9.10-23](https://github.com/AOSC-Dev/aosc-os-abbs/issues/1573) | [`stable`](https://github.com/AOSC-Dev/aosc-os-abbs/commit/34887186cd4d32149e32051f9c8fa873260dfef3) | [convert hang until 100% CPU 100% mem](https://github.com/ImageMagick/ImageMagick/issues/1408) |
| AOSA-2019-0016 | [`openjpeg`: patch](https://github.com/AOSC-Dev/aosc-os-abbs/issues/1574) | [`stable`](https://github.com/AOSC-Dev/aosc-os-abbs/commit/d966fd2332e83223665f57a6ffea1294eaf365c9) | [Out-of-bound left shift in opj_j2k_setup_encoder (src/lib/openjp2/j2k.c)](https://github.com/uclouvain/openjpeg/issues/1057), [Excessive Iteration in opj_t1_encode_cblks (src/lib/openjp2/t1.c)](https://github.com/uclouvain/openjpeg/issues/1059), [OPENJPEG null ptr dereference in openjpeg-2.3.0/src/bin/jp2/convert.c:2243](https://github.com/uclouvain/openjpeg/issues/1152) |
| AOSA-2019-0017 | [`systemd`: patch](https://github.com/AOSC-Dev/aosc-os-abbs/issues/1577) | [`stable`](https://github.com/AOSC-Dev/aosc-os-abbs/commit/c014b7686c44b70e30d99daffa9bf4597e9f670c) | [CVE-2018-16864 systemd: stack overflow when calling syslog from a command with long cmdline](https://bugzilla.redhat.com/show_bug.cgi?id=CVE-2018-16864), [CVE-2018-16865 systemd: stack overflow when receiving many journald entries](https://bugzilla.redhat.com/show_bug.cgi?id=CVE-2018-16865), [CVE-2018-16866 systemd: out-of-bounds read when parsing a crafted syslog message](https://bugzilla.redhat.com/show_bug.cgi?id=CVE-2018-16866) |
