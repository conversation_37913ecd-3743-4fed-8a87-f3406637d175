+++
title = "AOSCC 2022"
description = "AOSCC 2022 参会信息及议程安排"
date = 2022-09-13T23:38:16.388Z
[taxonomies]
tags = ["events"]
+++

# 参会信息

- 地点：社区 Discord #general 语音频道（[活动链接](https://discord.gg/bzYDZkbw?event=1013262604002672641)）。
- 日期：2022 年 9 月 17 日 (UTC +8)。
- 议程概览：
	- 社区项目开发报告及展示
	- 项目开发者闪电秀
	- 有关开源项目社区及生态系统的对话及讨论
	- 项目计划、工作流及开发工具相关讨论
	- 线下活动

# 议程安排

由于新冠疫情的持续影响，本年度 AOSCC 将于线上举行。议程于 UTC +8 时间早十时开始，并持续至晚间。议程如下：

如下议程表以 24 小时制标注时间。

| 讲者                    | 主题                                                                                                   | 时间          | 类型   |
|-------------------------|--------------------------------------------------------------------------------------------------------|---------------|--------|
| 白铭骢                  | 年度回顾；AOSC OS 代号、质量保障、开发工具、Retro 及 LiveKit 等                                        | 10:00 - 11:00 | 演讲   |
| 社区贡献者              | 闪电秀：社区项目展示                                                                                   | 11:00 - 12:00 | 闪电秀 |
| 吴楷阳                  | systemd-boot-friend：systemd-boot 启动菜单生成器                                                       |               |        |
| 傅孝元                  | aoscdk-rs：AOSC OS 安装程序                                                                            |               |        |
| 刘子兴                  | DeployKit（AOSC OS 安装程序）基于 Vue 实现的图形界面                                                   |               |        |
| Camber Huang            | Autobuild3 的测试框架                                                                                  |               |        |
| 上海社区友人            | 直播：飞腾 D2000/8 测试派对                                                                            | 12:00 - 14:00 | 活动   |
| 社区贡献者              | 社区外联及实习项目展示                                                                                 | 14:00 - 15:00 | 闪电秀 |
| 刘子兴                  | OSPP 2022 项目展示：重新实现 AOSC OS 软件包信息站点                                                    |               |        |
| 郑兴达及 Camber Huang   | PLCT 实验室实习项目：完善 AOSC OS RISC-V 移植                                                          |               |        |
| 苏运强                  | 芯联芯软件生态组在忙活什么？                                                                           |               |        |
| 社区贡献者              | 吐槽会：关于开源项目参与及生态系统未来                                                                 | 15:00 - 16:30 | 讨论   |
| 社区贡献者              | Core 10、工作流、质量保障、Autobuild3 特性、Retro 维护指南及贡献者认证等                               | 16:00 - [^1]  | 讨论   |

[^1]: 本议程结束时间视讨论进展而定。

## 自由活动/消磨时间

如有不感兴趣的话题或需进行其他讨论，请前往社区 Discord 其他语音/文字频道。
