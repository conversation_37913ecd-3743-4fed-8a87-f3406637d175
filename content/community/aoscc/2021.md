+++
title = "AOSCC 2021"
description = "AOSCC 2021 Agenda and Attendee Information"
date = 2020-09-14T23:38:16.388Z
[taxonomies]
tags = ["events"]
+++

# Attendee Information

- Location: AOSC's Discord #general Voice Channel ([Access Link](https://discord.gg/VYPHgt9)).
- Date: September 19 - 20, 2021 (UTC +8).
- Agenda at a Glance:
	- Progression report for community projects.
	- Lightning rounds by project developers.
	- Discussions on future development, workflow, and tooling adjustments

*AOSCC 2020, its lectures, discussions, and workshop activities will be delivered/held in Mandarin Chinese. If you do not speak this language, please note at time of each activity and we will try our best to accomodate (most likely in English).*

# Agenda

Due to the continued effect of the COVID-19 pandemic, this year's AOSCC will be held online. Apart from the switch to an online venue, as was the case with AOSCC 2020, we have shortened and simplified this year's agenda. In particular, each day's agenda will begin at 10:00 (UTC +8) and will end in the evening. All agenda will be held consequtively at our Discord #general voice channel in accordance with the schedule below:

- The following schedule is presented in 24-hour time format.
- Agenda formats:
	- DIS: Discussion
	- LGN: Lightning Rounds
	- TLK: Talk

## Day 1 (September 19th) 

| Speaker           | Topic                                                                                    | Time          | Format |
|-------------------|------------------------------------------------------------------------------------------|---------------|--------|
| Mingcong Bai      | Year in Review; AOSC OS: Codename, QA, Tooling Improvements, Retro, LiveKit, etc.        | 10:00 - 11:00 | TLK    |
| Zixing Liu        | In-Depth Look at Tooling Improvements                                                    | 11:00 - 12:00 | TLK    |
| Leo Shen          | Beyond APT: A Demonstration and Technical Presentation on APM                            | 14:00 - 15:00 | TLK    |
| WordlessEcho      | Review of KDE L10n During OSPP 2021                                                      | 15:00 - 15:15 | LGN    |
| Mag Mell          | Project Demonstration: Meowdict and `rime-schema-manager`                                | 15:15 - 15:30 | LGN    |
| Kaiyang Wu        | Project Demonstration: systemd-boot-friend                                               | 15:30 - 15:45 | LGN    |
| Zixing Liu        | Project Demonstration: `aosc-findupdate`, `aoscbootstrap-rs`, and `aoscdk-rs`            | 15:45 - 16:00 | LGN    |
| Pak Squad         | Core 9 Features, Python 2 Retirement, Multi-branch Software Maintenance (Node.js, etc.)  | 16:00 - [^1]  | DIS    |

[^1]: This agenda will end when discussion concludes in the evening.

## Day 2 (September 20th)

| Speaker           | Topic                                                                                | Time          | Format |
|-------------------|--------------------------------------------------------------------------------------|---------------|--------|
| Mingcong Bai      | AOSC OS/Retro Progression Report, Demonstration, and Future Outlook                  | 10:00 - 11:00 | TLK    |
| Zixing Liu        | Ciel Remote Features and Automation Pipedreams                                       | 11:00 - 12:00 | TLK    |
| Community-Wide    | Just for Fun: AOSC OS/Retro Physical Media                                           | 14:00 - 15:00 | DIS    |
| Pak Squad         | Workflow and Tooling Feedback, Impromptu Topics                                      | 15:00 - [^2]  | DIS    |

[^2]: This agenda will end when discussion concludes in the evening.

## Passing Time

If the current topic does not interest you, please head to other text/voice channels on our community Discord.
