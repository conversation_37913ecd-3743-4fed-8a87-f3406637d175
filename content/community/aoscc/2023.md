+++
title = "AOSCC 2023"
description = "AOSCC 2023 Agenda and Attendee Information"
date = 2023-05-26
[taxonomies]
tags = ["events"]
+++

{% card(type="info") %}

- Per request from the ShanghaiTech University, attendees must register their personal information before arrival on campus.
- If you would like to attend AOSCC 2023 in-person, please [register here](https://forms.gle/vc8sd3yah7eMNmoP7).

{% end %}

# Conference Facts

- Location: ShanghaiTech University Zhangjiang Campus (School of Information Science and Technology, Lecture Hall 1C-101, 393 Middle Huaxia Road, Pudong New District, Shanghai) [Campus Map](https://map.shanghaitech.edu.cn/) [direction](https://webresources.aaaab3n.moe/2023/07/Screenshot_20230710_224535.png)
	- It is recommended to enter through the East Gate of ShanghaiTech University (No.1 Zhongke Road, Pudong, Shanghai).
- Date: July 15 - 16, 2023 (UTC +8)
- Agenda:
	- Community (project) updates, discussions, and polls.
	- Seminars on free and/or open source software and hardware industries.
	- Other skill-sharing activities.
- Souvenirs:
	- AOSCC 2023 Sticker Pack (Pages A/B), those who could not attend in-person may contact for a mailed copy after the conference.
	- Tote bags with AOSC mascots and graphics.
- Merchandises:
	- Graphics T-Shirts.
- Lucky Draws:
	- 5 * IBM ThinkPad X41 (AOSC OS/Retro + Windows XP)
	- 4 * Redmi 2 (AOSC OS)
	- 1 * L5A2 Motherboard (Loongson 3A5000, AOSC OS)

# Agenda

AOSCC 2023 will last two (2) days. All agenda items below are listed in the 24-hour time format.

Legends for program types are as follows:

- LEC: Lectures and keynotes.
- DIS: Discussions.
- ACT: Activities.
- LGN: Lightning sessions.

All programs will be delivered in Chinese (Mandarin).

## Day 1

| Speaker(s)              | Topic                                                                         | Time Slot     | Type   |
|-------------------------|-------------------------------------------------------------------------------|---------------|--------|
| Attendees               | Check-ins and Greetings                                                       | 8:00 - 11:00  | ACT    |
| Mingcong Bai            | Year in Review                                                                | 11:00 - 12:00 | LEC    |
| Attendees               | Device demo session, lunch, and break period                                  | 12:00 - 14:00 | ACT    |
| -                       | Nomination and Poll: Core 11 "K" Codename                                     | 14:00 - 14:30 | ACT    |
| -                       | Lucky Draw: IBM ThinkPad X41                                                  | 14:30 - 15:00 | ACT    |
| Community Contributors  | Live: AOSC OS Self-Review                                                     | 15:00 - 16:00 | ACT    |
| Xiaoyuan "Mag Mell" Fu  | Omakase (Next-Generation AOSC OS Package Manager): Origins, Demo, and Outlook | 16:00 - 16:30 | LEC    |
| Community Contributors  | Ranting Session: On FOSS, Project Participation, and its Future               | 16:30 - [^1]  | DIS    |

[^1]: This session will end depending on session progression.

## Day 2

| Speaker(s)                  | Topic                                                                         | Time Slot     | Type   |
|-----------------------------|-------------------------------------------------------------------------------|---------------|--------|
| Community Contributors      | Project Exhibits                                                              | 10:00 - 11:00 | LGN    |
| Zixing Liu, Cinhi Young     | Automation Infrastructure (Ciel RPC, Containers, and GitHub Workflows）       | \~ 45 minutes |        |
| Mingcong Bai, Icenowy Zheng | New Ports: AOSC OS on LoongArch, MIPS64 R6, and RISC-V                        | \~ 15 minutes |        |
| LoongArch Community and Loongson Technology Employees | Of Loongson and You: Ask Us Anything                | 11:00 - 12:00 | DIS    |
| -                           | Lucky Draw: L5A2 Boards (Loongson 3A5000)                                     | 12:00 - 12:30 | ACT    |
| Attendees                   | Device demo session, lunch, and break period                                  | 12:30 - 14:00 | ACT    |
| Wu Wei                      | RISC-V Software Ecosystem: Full Steam Ahead                                   | 14:00 - 14:30 | LEC    |
| Community Contributors      | Community Outreach and Internship Showcase                                    | 14:30 - 15:30 | LGN    |
| YunQiang Su                 | CIP United Outreach                                                           | \~ 15 minutes |        |
| Yichi Zhang                 | Showcase: GeekPie's New Service Architecture                                  | \~ 15 minutes |        |
| Zixing Liu                  | Canonical Outreach                                                            | \~ 15 minutes |        |
| Changyuan Yu                | Shanghai Linux User Group (SHLUG) Outreach                                    | \~ 15 minutes |        |
| Community Contributors      | Free Discussion: Topics for Concerned Community Members                       | 15:30 - 16:30 | DIS    |
| -                           | AOSC OS: Distro or OS? Traditional Software Repositories and its Future       | \~ 30 minutes |        |
| -                           | Language Barrier: Chinese-Driven Community and Language Standards             | \~ 30 minutes |        |

## Passing Time

Before and after conference scheduling, or during topics that does not interest you, we suggest the following activities for you to pass time.

- Enjoying AOSC OS and AOSC OS/Retro demo devices.
	- ITAIC (信创) Devices: Phytium, Loongson, Kunpeng, and RISC-V devices.
	- Retro devices: IBM ThinkPad X41, etc.
- Obtaining stickers and other souvenirs.
- Do whatever you wish!

*Note: Please be mindful of your volume whilst programs are underway.*

# Lodging Guide

Please review the [Chinese version of this page](https://wiki.aosc.io/zh/community/aoscc/2023/#zhu-su-zhi-nan) for more information.

# Leave of Absence

For those who are currently employed or are partaking in paid projects, please refer to the following templates for Leave of Absence.

- [In English](https://docs.google.com/document/d/1GkCdseMtRllGBpt7KWlgP5SUbX17DO81zB72PPHXKck/)
- [In Simplified Chinese](https://docs.google.com/document/d/1296FHRsfwcT9NNsqtS2I-LT2LaCLrpVcMxJcl3Lhzos/)
