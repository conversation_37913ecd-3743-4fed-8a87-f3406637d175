+++
title = "AOSCC 2016"
description = "Location, Agenda, and Other Information for AOSCC 2016"
date = 2020-04-03T03:35:07.225Z
[taxonomies]
tags = ["events"]
+++

AOSCC 2016 is the second year of AOSC's community conference. AOSCC is a
community centric conference with an emphasis to:

- Make reports and reflections of the past year's work and involvement;
- Guide and train new members with involving in community development;
- Meet new and old friends (of course) in and around the community;

Location
--------

Room 406, Administration Building, 100 Haike Rd, Pudong New District, Shanghai, China.

上海浦东新区海科路 100 号行政楼 406 室


At a glance
-----------

AOSCC 2016 will take place on July 16-18, 2016 in ShanghaiTech University -
Zhangjiang Campus.

- Day 1:
  - Annual report (Mingcong Bai)
  - Assorted votes and discussions for Core 4
    - Codename
    - Features
  - Voting for AOSCC 2017: location only
  - Post-conference: GPG Signing Party (Howard Xiao)

- Day 2:
  - Speeches/Keynotes
    - Community developers (Anth<PERSON> Starter, Auch?)
    - Geekpie members (if possible)
    - Kick-off and announcement of Day 3 workshop
      - Packaging
      - Wallpaper (to be announced prior to the conference)
      - L10n (if possible)

- Day 3:
  - Workshop
    - Introduction to packaging
      - Interpretation of documentation of `autobuild3` and `abbs`
      - Hands-on experience
      - PR's to be made to the `aosc-os-abbs` tree
    - Wallpaper
      - Default wallpaper for 2016-2017 AOSC OS releases
    - L10n
      - Introduction to L10n
      - Possible hands-on (not likely)
    - Lucky-roll and give-aways
      - AOSCC 2016 sticker pack (free to take)
      - CrossOver licenses and Raspberry Pi 3 (\*) give-aways
      - CrossOver licenses gifted to Codename and Wallpaper winners

**Note:**
All Raspberry Pi 3 handed out during the conference are sponsored by Yunpian, and
are provided with AOSC OS pre-installed plus with a package named `yunpian-sdk-python`:
A Python SDK for Yunpian web services. This package is provided as a sponsored extra,
and can be removed freely if found unnecessary.

Time
----

Day 1 of the conference will set to start at noon to compensate for
transportation of attendees living outside of Shanghai. Each day following will
start at 10 a.m.

Conference ends at approximately 6 p.m. each day, no exact time will be set
to allow for variabilities in schedules.

Detailed agenda
---------------

Here below is a detailed agenda of AOSCC 2016, with time and specific event
information.

### Day 1: Reflections and discussions

Day 1 will be focused around reports and reflections of the past year and some
initial planning of the next. An estimated time table is presented below.

| Time         | Event information                                                                  |
| ------------ | ---------------------------------------------------------------------------------- |
| 12:00 P.M.   | Reception, Room 406 at the Administration Building                                 |
| 1:30 P.M.    | Mingcong Bai: Annual report                                                        |
| 2:30 P.M.    | Mingcong Bai, Xingda Zheng: A.M.A (Ask Me Anything) about AOSC OS                 |
| 3:30 P.M.    | Mingcong Bai: Polls and discussions about the coming year                          |
| 5:30 P.M.    | Junde Yhi: lucky draws                                                          |
| 6:00 P.M.    | Tianhao Xiao: post-conference GPG signing party                                    |

During Day 1, reports and reflections will contain reviews of community
involvement, project achievements, and stuff that are more or less left behind
(and therefore should be taken care of in short order).

Planning and discussions are two other important component of Day 1, which will
consist of:

- Discussions: some features or new stuff are worth discussing over before the
real "do", so we will do that here.
- Votes/Polls:
  - Codename of next year's AOSC OS development.
  - Next year's default wallpaper for AOSC OS.
  - Next year's AOSCC location.

Junde Yhi will then go on to host a lucky draw for licenses for CodeWeavers' CrossOver.

### Day 2: Knowledge worth sharing

If fun is your thing, Day 2 will certainly be your day. Day 2 has a strong
emphasis on sharing and clashing of ideas and knowledge. During the day, people
from around the community will come in with their prepared speeches, there might
just be your kind of thing inside!

| Time         | Event information                                                                  |
| ------------ | ---------------------------------------------------------------------------------- |
| 10:00 A.M.   | Reception at same location, chatters and late-morning greetings                    |
| 11:00 A.M.   | gumblex: Recreating artwork with Inkscape                                          |
| 12:00 P.M.   | Lunch break                                                                        |
| 1:00 P.M.    | Junde Yhi, Zixing Liu: Distributed and CI functionality of ABBS                    |
| 2:00 P.M.    | Jiahe Shi (GeekPie Association): Setting up development environment on campus      |
| 3:00 P.M.    | Junde Yhi: More on Anthon Starter                                                  |
| 3:30 P.M.    | Xingda Zheng: Way to GNU/Linux on ARM                                              |
| 4:00 P.M.    | Mingcong Bai, Zixing Liu: before you start (tomorrow's workshop), A.M.A. regarding working with packaging, localization (l10n), and everything else! |
| 5:00 P.M.    | Mingcong Bai: Lucky draws                                                          |

Today's luck draw will still be hosted by Mingcong Bai with Raspberry Pi 3's
sponsored by Yunpian (and of course, they come pre-configured with AOSC OS).

### Day 3: Hands-on experience

Day 3 will be all about hands-on experience with work around free and open
source projects, this will be day-long workshop with multiple parallel stations
available to attendees.

| Time         | Event information                                                                  |
| ------------ | ---------------------------------------------------------------------------------- |
| 10:00 A.M.   | Reception at same location, chatters and late-morning greetings                    |
| 11:00 A.M.   | Start of workshop                                                                  |
| 12:00 P.M.   | Lunch break                                                                        |
| 3:00 P.M.    | Announcement of wallpaper and codename winners                                     |

Day 3 workshops are available as the following stations:

- Mingcong Bai: hands-on with packaging for AOSC OS.
- Zixing Liu: introduction and hands-on to contributing localization to free
and open source software projects.
- Junde Yhi: AST's Startup Toolkit development meet-up (developers and hobbists welcome).
- Xingda Zheng: Ways to Kernel patch submission and ARM kernel development (short).

Winners of wallpaper and codename contests will be awarded with a copy of CodeWeavers' CrossOver.

Accomodation
------------

For participants' convenience, we've been searching for economical hotels around the meeting place.

- [JinJiang Inn (Pinshang) (zh_CN)](http://hotels.ctrip.com/hotel/1838785.html)
- [Qiulinge Inn (zh_CN)](http://hotel.elong.com/shanghai/90933786)

For reference only.

Sponsors
------------

We are glad to have the following sponsors for the conference.

- [ShanghaiTech University](http://www.shanghaitech.edu.cn/eng/)
- [Yunpian](https://www.yunpian.com)
- [CodeWeavers](https://www.codeweavers.com/)

Want to be a sponsor? Contact us!
