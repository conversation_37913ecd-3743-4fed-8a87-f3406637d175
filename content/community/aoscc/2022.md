+++
title = "AOSCC 2022"
description = "AOSCC 2022 Agenda and Attendee Information"
date = 2022-09-13T23:38:16.388Z
[taxonomies]
tags = ["events"]
+++

# Attendee Information

- Location: AOSC's Discord #general Voice Channel ([Event Link](https://discord.gg/bzYDZkbw?event=1013262604002672641)).
- Date: September 17, 2022 (UTC +8).
- Agenda at a Glance:
	- Progress report and demo programs for community projects.
	- Lightning rounds by project developers.
	- Conversation on FOSS communities and ecosystem.
	- Discussions on future development, workflow, and tooling adjustments

*AOSCC 2022, its lectures, discussions, and workshop activities will be delivered/held in Mandarin Chinese. If you do not speak this language, please note at time of each activity and we will try our best to accomodate (most likely in English).*

# Agenda

Due to the continued effect of the COVID-19 pandemic, this year's AOSCC will be held online. Apart from the switch to an online venue, as was the case with AOSCC 2021 and 2022, we have shortened and simplified this year's agenda. AOSCC programs will begin at 10:00 (UTC +8) and will end in the evening. All agenda will be held consequtively at our Discord #general voice channel in accordance with the schedule below:

- The following schedule is presented in 24-hour time format.
- Agenda formats:
	- DIS: Discussion
	- EVT: Event
	- LGN: Lightning Rounds
	- TLK: Talk

| Speaker(s)              | Topic                                                                                                  | Time          | Format |
|-------------------------|--------------------------------------------------------------------------------------------------------|---------------|--------|
| Mingcong Bai            | Year in Review; AOSC OS: Codename, QA, Tooling Improvements, Retro, LiveKit, etc.                      | 10:00 - 11:00 | TLK    |
| Community Contributors  | Lightning Rounds: Project Showcase                                                                     | 11:00 - 12:00 | LGN    |
| Kaiyang "OriginCode" Wu | systemd-boot-friend, A Menu Generator for systemd-boot                                                 |               |        |
| Xiaoyuan "Mag Mell" Fu  | aoscdk-rs, AOSC OS' Standard System Installer                                                          |               |        |
| Zixing "liushuyu" Liu   | DeployKit's Upcoming Vue-based GUI                                                                     |               |        |
| Camber Huang            | Autobuild3's Testing Framework                                                                         |               |        |
| Local Party at Shanghai | Live from Shanghai: Phytium D2000/8 Test Fest                                                          | 12:00 - 14:00 | EVT    |
| Community Contributors  | Community Outreach and Internships                                                                     | 14:00 - 15:00 | LGN    |
| Zixing "liushuyu" Liu   | OSPP 2022 Project: Re-implementing the AOSC OS Packages Site                                           |               |        |
| Icenowy Zheng           | PLCT at the ICT: Bringing Up the AOSC OS RISC-V Port                                                   |               |        |
| Yunqiang Su             | Meet CIP United's Software Ecosystem Workgroup                                                         |               |        |
| Pak Squad               | An Honest Conersation on FOSS Project Involvement and the Ecosystem's Future                           | 15:00 - 16:30 | DIS    |
| Pak Squad               | Core 10, Workflows, QA, Autobuild3 Changes, Retro Maintenance, Contributor Certification, etc.         | 16:00 - [^1]  | DIS    |

[^1]: This agenda will end when discussion concludes in the evening.

## Passing Time

If the current topic does not interest you, please head to other text/voice channels on our community Discord.
