+++
title = "AOSCC 2018"
description = "Location, Agenda, and Other Information for AOSCC 2018"
date = 2020-05-04T03:35:07.225Z
[taxonomies]
tags = ["events"]
+++

Due to issues with venues, AOSCC 2018 will be held online. AOSCC 2018 will be
organised via Internet chat groups (Telegram and IRC), spanning two weekends.
This year's AOSCC will focus specifically on matters surrounding AOSC project
development, and thus will not include any topic-based talks - though
community-wide polls will be organised.

Assembly Form and Organisation
------------------------------

- The meetings and discussions will take place on an event-specific Telegram
  group chat, which synchronises with another IRC/Matrix channel. Attendees will
  be able to see each other.
- Discussions will *not* take place simultaneously.
- Community polls will be organised specific to the two ways of participation.
    - Telegram users will cast their votes via a Telegram In-Line Bot (@vote).
    - IRC/Matrix users will cast their votes via messages.
- Discussion language will be Chinese Simplified or Traditional. The use of any
  other language(s) may hinder your ability to participate.

Time and Dates
--------------

- AOSCC 2018 will span four days, based on China Standard Time (Asia/Shanghai).
    - July 21st: 9:00 - 12:00, 21:00 - 0:00.
    - The same time applies for July 22nd, July 28th, and July 29th.
- Foreign attendees will need to adjust according to their timezones.

Community-wide Polls
--------------------

- As per tradition of AOSCC, several community-wide votes are planned.
    - Vote on the default wallpaper(s) of AOSC OS releases of the coming year
      (July 29th).
    - Vote on the default codename of the next AOSC OS Core series (July 21st).
- However, breaking the tradition, as we will not be voting on the meeting
  location of AOSCC 2019 - due to the increasing difficulties in obtaining
  venue in China.
    - Instead, those interested should nominate venues which they have
      communicated with, and, on a later date, the community will vote on the
      next meeting location based on the available choices.

Discussion Schedule
-------------------

The schedule below assumes that all discussion topics should take at most an
hour, it could be adjusted to suit the needs of a particular topic. However,
starting time for each topic should not be altered in principle.

| Date      | Time          | Topic                                                            | Discussion Leader                               |
|-----------|---------------|------------------------------------------------------------------|-------------------------------------------------|
| July 21st | 9:00 - 9:30   | Fun: Casual Conversations and Ice-breaking                       | N/A                                             |
| July 21st | 9:30 - 10:30  | AOSC OS: Moving from Monthly Waves to Seasonal Waves; Milestones | [Mingcong Bai](https://github.com/MingcongBai/) |
| July 21st | 11:00 - 12:00 | AOSC OS: Packaging Workflow and Toolchain Enforcement            | [Daming Yang](https://github.com/LionNatsu/)      |
| July 21st | 21:00 - 22:00 | AOSC OS: Security Infrastructure and Advisories                  | [Zero King](https://github.com/l2dy)            |
| July 21st | 22:15 - 23:15 | AOSC OS: Optimisation Overlay in Implementation                  | [Junde Yhi](https://github.com/lmy441900/)      |
| July 21st | 23:30 - 0:00  | Poll: Codename of AOSC OS Core 6 (starting with "F")             | [Mingcong Bai](https://github.com/MingcongBai/) |
| July 22nd | 9:30 - 10:30  | AOSC OS: Conversations on the Future of System Delivery          | [Mingcong Bai](https://github.com/MingcongBai/) |
| July 22nd | 11:00 - 12:00 | AOSC OS: Contiguous Integration of System Delivery               | [Zixing Liu](https://github.com/liushuyu/)      |
| July 22nd | 21:00 - 22:00 | AOSC OS: On Implementation and Enforcement of Source Checksum    | [Daming Yang](https://github.com/LionNatsu/)      |
| July 22nd | 23:00 - 0:00  | AOSC OS: Moving from Monthly Waves to Seasonal Waves; Milestones (Session 2) | [Mingcong Bai](https://github.com/MingcongBai/) |
| July 28th | 9:30 - 10:30  | AOSC OS: On the New Features of Core 6                           | [Mingcong Bai](https://github.com/MingcongBai/) |
| July 28th | 11:00 - 12:00 | Community: Store and Economics (Anthon Online Souvenir Centre)   | [Shunran Zhang](https://github.com/StephDC/)      |
| July 28th | 21:00 - 22:00 | Projects: Maintenance and Forking of Upstream Projects           | [Icenowy Zheng](https://github.com/Icenowy/)    |
| July 28th | 22:30 - 23:30  | AOSC OS: Let's Talk about Themes and Interface Customisation     | [Mingcong Bai](https://github.com/MingcongBai/) |
| July 29th | 9:00 - 10:00  | AOSC OS: Package (and their Arch-specific) Variants (Autobuild3) | [Icenowy Zheng](https://github.com/Icenowy/)    |
| July 29th | 10:00 - 11:30 | Reserved for Additional Topics [^1]                              | N/A                                             |
| July 29th | 11:30 - 12:00 | Poll: Default Wallpaper of AOSC OS Releases in 2018 - 2019       | [Mingcong Bai](https://github.com/MingcongBai/) |
| July 29th | 21:00 - 0:00  | AOSC OS: Moving from Monthly Waves to Seasonal Waves; Milestones (Session 3) | [Mingcong Bai](https://github.com/MingcongBai/) |

- [^1] Or otherwise treated as a break.

Rules and Notes on Participation
--------------------------------

- Given the nature of an online assembly, a discussion leader has been set in
  place to regulate the direction/steering of the topic - all participants
  should observe the directions of the particular leaders, violators will be
  removed for the topic until the next one.
- Discussion leaders should segment their discussion topics to encourage more
  conversation, and avoid utilising more than 10 minutes on a speech.
- Further discussion topics, if introduced, should be segregated in contrast
  to the immediate topics to avoid confusion.
  
Chat Logos
----------

[AOSCC 2018 Main Channel](https://github.com/AOSC-Dev/aoscc/raw/master/2018/assets/chat-logos/aoscc.png)

[AOSCC 2018 Polls Channel](https://github.com/AOSC-Dev/aoscc/raw/master/2018/assets/chat-logos/aoscc-polls.png)
