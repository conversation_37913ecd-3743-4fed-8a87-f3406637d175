+++
title = "[众筹成功] 升级社区主力 AMD64 编译服务器 (22333)"
draft = false
+++

本文描述升级社区主力 AMD64 编译服务器 (22333) 的计划。

# 导言

在服役超过三年后，我社主力 AMD64 编译服务器出现了性能不足（尤其在 RISC-V 和 MIPS R6 等依赖 Qemu 构建的架构使得 PorterAle 模拟构建服务器负载饱和的情况下）和部件接近使用寿命（作为构建暂存盘的 Intel SSD 750 已写入超过 350TiB，超过厂商标称寿命 100TiB 以上）等情况。考虑到现有 AM4/X570 平台对处理器核心数量和内存容量的制约，我们计划升级平台，以便满足如下主要目标：

- 通过升级处理器和平台硬件提高构建效能
- 内存扩容，使用内存作为构建暂存盘 (Scratch Disk on RAM)
- 更换 SSD 等寿命耗尽的部件

# 预算计划

本次升级预算预计约 17000 人民币（目前部件报价为 16612.99 人民币，我们将在购置部件后退还余款）。

+ 主要平台部件
    - 主板：1 × 技嘉 MZ31-AR0
    - 处理器：1 × AMD EPYC 7R32（48 核心，96 线程）
    - 内存：16 × 三星 M393A8K40B21-CTC0Q（64GiB，DDR4 2400MT/s Registered ECC；共 1TiB 容量）
+ 存储
    - 系统及本地源存储：1 × Intel SSD 750 (1.2TB)
+ 配件
    - 散热器：1 × 金钱豹 (Coolserver) SP3 P42
    - 机箱、散热硅脂等

# 日程计划

- 2023 年春：多渠道筹款，由 Lain Yang 负责购置（按照上述部件从上到下，在筹集到相应款项即购买；美元捐款使用西联转账至国内账户）
- 2023 年夏：由王江津运输至美国白铭骢家中部署

# 负责人

- Lain Yang <<EMAIL>> ：采购负责人
- 王江津 <<EMAIL>>：运输大队长
- 白铭骢 <<EMAIL>>： 托管方
