+++
title = "[COMPLETE] Purchase Apple Mac mini M1 as AArch64 Build Server"
+++

This is a proposal to crowdsource a Apple Mac mini with M1 SoC to replace the current AArch64 BuildBot located in Guangzhou (Relay 24242, also a Mac mini M1).

# Rationale

By purchasing an additional Mac mini M1, we plan to effectively relocate the current AArch64 BuildBot (Relay 24242) to South California, United States of America. By doing so, we expect to see more reliable and faster Internet access, and we will utilise the retired Mac mini M1 in Guangzhou for desktop testing targetting the AArch64 port, and especially for the Apple Silicon platforms.

# Budget

+ Apple Mac mini with M1 SoC ($798.6 via Apple EPP discount)
  - M1 SoC with 8-core CPU and 8-core GPU
  - 16GiB of RAM
  - 256GiB of NVMe storage

## Notes

- [CodeWeavers](https://www.codeweavers.com/), the creator of the CrossOver software suite based on Wine, partly funds this crowdsource project with a single-time $500 donation.
- You may fund this hardware purchase via our [GitHub Sponsors](https://github.com/sponsors/AOSC-Dev) page, or contact <PERSON><PERSON><PERSON> (contact information listed below) for alternative payment methods.

# Timeline

- November 2021: Receipt of $500 donation from CodeWeavers.
- End-of-Year 2021: Coordinator to complete purchase and deployment.

# Contacts

- <PERSON>cong <PERSON> <<EMAIL>>, purchase contact and server host.
