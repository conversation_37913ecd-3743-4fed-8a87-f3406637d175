+++
title = "GSoC 2023 Projects"
description = ""
date = 2023-01-29
[taxonomies]
tags = ["gsoc"]
+++

Welcome! This page outlines our community's GSoC 2023 project. You may pick one and contact the corresponding leader, or you may discuss your interested topic with us in either our [IRC channel][irc], [Telegram group][tg], [Discord server][discord], or [mailing list][mlist].

[irc]: ircs://irc.libera.chat:6697/aosc
[tg]: https://t.me/joinchat/BMnG9zvfjCgZUTIAoycKkg
[discord]: https://discord.gg/VYPHgt9
[mlist]: mailto:<EMAIL>

# DeployKit GUI

Implement the GUI for DeployKit, our AOSC OS installer. This will be done in accordance with our [design mock-up](https://github.com/AOSC-Dev/aoscdk-rs/tree/gui/mockups)

- Size: Large
- Mentor: <PERSON><PERSON><PERSON>, T.B<PERSON>D.
- Mentor contact: <EMAIL>, or Telegram @liushuyu
- Project Requirements:
  - Finish GUI in accordance with the design mock-up.
  - Implement backend logic.
- Technical Requirements:
  - Rust.
  - Web-based interface: WRY + Tao.
  - Frontend framework: Vue.js + Vite.
- Related Repositories:
  - https://github.com/AOSC-Dev/aoscdk-rs
  - https://github.com/AOSC-Dev/deploykit-ui
- License: MIT + Apache 2.0 dual license.
