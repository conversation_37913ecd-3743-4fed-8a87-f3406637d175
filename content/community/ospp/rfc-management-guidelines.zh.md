+++
title = "OSPP 项目管理指南 (RFC)"
description = ""
date = 2022-03-16
[taxonomies]
tags = ["ospp"]
+++

自 2020 年起，本社区每年积极参与中科院软件所组织的暑期 OSPP 活动。在相关活动中，本社区任命导师，OSPP 活动主办方派遣学生，导师与学生合作完成指定项目课题。参与该活动旨在为社区吸引新贡献者并为社区贡献者提供专业经验。社区导师及学生亦可通过双方工作赚取一定量的经济收入。

本文旨在记载本社区在三方对接、具体项目执行管理的问题上的一致意见，供导师和学生参考。

# 社区对 OSPP 活动的期待

- 持续贡献：充分利用该活动为社区引入新鲜血液。

# 风险点总结

基于先前社区参与的经验，可以总结如下风险点：

- 需求管理：产品需求是所有严肃的软件项目的首要问题。
- 进度管理：开发时间有限且时效性要求高，所以必须重视进度。
- 技术选型：技术选型需要考虑导师的指导能力及和社区的后续维护能力。

# 基本规定

本社区对参与 OSPP 活动的导师及学生的基本义务作出如下基本规定：

1. 导师及学生须仔细阅读并遵守主办方的各项活动及工作规则。
2. 导师有沟通、听取贡献者意见及考核学生的义务。
   1. 在前期准备时，导师须以社区贡献者例会的形式讨论项目筹备的具体（如需求、选型及考核等）问题，并积极吸取、落实其他贡献者的意见。
   2. 在项目日常工作过程中，导师须定时在主群组与社区成员沟通项目进度及考核情况。
   3. 考核时，导师应于社区主群组公开考核标准及反馈意见，于贡献者群组讨论考核结果，并以会议记录的形式向其他社区成员公开讨论过程。
3. 学生有定时完成工作及与导师沟通的义务。
   1. 学生须与导师沟通并确定具体工作目标及日程，并尽一切努力按时、高质量地完成指定目标。
   2. 学生须及时答复导师在项目工作方面的各项沟通，如有需要暂时离开，须与导师协商请假事宜；如导师在 3 日内未获得学生答复，则视作学生主动放弃项目，并在主办方下一阶段考核宣告学生考核不通过。
   3. 除非有碍于个人隐私（如请假时），学生须于社区主群组公开地与导师沟通一切项目工作事宜。

# 前期准备

## 工作流程

- 社区内部确定项目需求和导师人选
- 社区内部划定项目边界
- 撰写第一组前期文档
  - 《产品需求文档》（列明需求）
  - 《软件设计规范文档》（规定工作边界，设计具体行为）
- 向 OSPP 主办方提交项目信息
- 接待学生候选人（含考核）
- 撰写第二组前期文档
  - 《技术选型文档》（指明技术栈和依赖）
- 敲定学生人选

## 确定需求

软件产品设计过程中应当形成一系列明确需求。为此，在开工前建议参考如下问题以确定项目的基本需求：

- 由谁使用？用于什么场景？需满足什么用户目标？
- 有哪些核心功能及次要功能？
- 在功能目标之外，有哪些其他目标（文档、manpage、教程……）？

在需求商议阶段，导师应当主导相关讨论，本社区内对此项目关心的老伙计们可以发表意见。需要形成《产品需求文档》。

## 明确边界

在需求确定后，需要针对项目性质及规模确定边界。为此，在开工前建议参考如下问题以确定项目的基本性质及规模边界：

- 项目生命周期（一年、五年、十年……）？学生和导师需要考虑如何协助后续贡献者参与及维护该项目。
- 项目针对何种应用场景？导师需确定项目影响到的贡献者及用户人群，并考虑收集及参考意见。
- 需要依赖何种软件依赖及知识技能？导师需首先确定自身知识及业务能力，明确具体应用场景及技术性依赖后，方可设计考核标准。

## 撰写第一组前期文档

导师主导撰写《产品需求文档》和《软件设计规范文档》。文档的内容应当反映导师和本社区内对此项目关心的贡献者的一致意见。此外，项目管理志愿者可以协助维护文档管理的基础设施，在导师的邀请下参与文档撰写。

边界方面的决定可以不独立成《软件设计规范文档》，仅在《产品需求文档》中添加专门章节加以记载。

## 向 OSPP 主办方提交项目信息

由社区代表协调全体导师整理信息，并集中向 OSPP 主办方提交项目信息。提交的具体信息以当年 OSPP 活动规则为准。

## 接待学生候选人

导师需考核学生候选人的技术及业务能力。具体考核标准及选拔结果，均应由导师收集并咨询社区贡献者意见。

## 技术选型

合理的技术选型是软件产品顺利开发、持续维护的必要前提。为此，我们需要考虑以下问题，并对项目设计及日程进行具体化调整：

- 导师本人擅长哪些技术栈？
- 社区里的其他活跃开发者能够协助维护哪些技术栈的源代码？
- 学生本人擅长哪些技术栈？

导师应定时与有关贡献者讨论选型方面的具体决定并整理共识。

## 撰写第二组前期文档

导师主导撰写《技术选型文档》，记载社区贡献者在关于技术选型问题的共识。

# 工作组织

## 日常沟通

在项目的日常工作中，一切沟通应于社区主群组（可选 Telegram，IRC，Matrix 或 Discord）进行，鼓励学生在工作过程中与社区其他成员沟通，并协助其后续继续参与社区项目的开发及维护工作。导师亦有义务及时沟通项目进展情况及考核结果（见下）。

## 目标细化

导师应考虑并分析项目是否可以分阶段交付，并在可能时将项目计划拆分为多个阶段 (phase) 。每阶段的设计应包含以下要素：

- 合理工期？
- 具体目标？
- 验收标准？

不得在前一阶段验收通过前开启下一阶段。每一阶段的验收标准及结果均应以导师组织其他贡献者的讨论结果为准，导师应充分考虑其他贡献者的意见。

## 进度追踪

每个阶段亦可细分为多个里程碑 (milestone) ，每个里程碑周期长度应为 1 - 2 周。如阶段目标较少或较小，可设置单一里程碑或目标。项目阶段应体现为社区 GitHub 组织的 OSPP 项目仓库中的 Issue，而每个 Issue 应使用 GitHub 的 Milestone 功能对应每个里程碑。

导师应每周在社区主群组中与学生沟通进度，允许社区成员观摩、提问、发表意见或提供帮助。

# 后期文档

结项前，如项目需学生或社区贡献者继续维护，应在上文所述前期文档以外撰写如下后期文档：

- 用户手册（指导用户该如何使用，作为 manpage 或 wiki）
- 开发者手册（超出技术选型文档的范畴但对将来的开发者必要的信息）

此外，导师应当撰写《结项报告》，向社区成员和 OSPP 主办方公示本项目的成果。

# 事故处理

## 质量缺陷

本社区不是商业公司，不对项目日程及完成度进行硬性规定。在相对合理的范围内，应优先保障软件质量，并定时利用软件质量保障措施（白盒测试、黑盒测试及单元测试等）。

## 交付延期

如学生未能在规定时间内完成阶段性目标，则学生可申请延期，但导师必须调查清楚延期的原因，并及时向社区主群组报告具体情况。
