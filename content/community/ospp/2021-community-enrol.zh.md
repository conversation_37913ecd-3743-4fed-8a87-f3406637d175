+++
title = "开源软件供应链点亮计划暑期 2021 社区报名"
description = ""
date = 2021-03-21
[taxonomies]
tags = ["ospp"]
+++

> 本文档仅供协作和透明。

# 社区报名模板

## 报名信息摘要

- 社区或开源项目名称：安同开源社区（Anthon Open Source Community, 简称 AOSC）
- 社区官网网址： https://aosc.io/
- 联系人：_(Junde Yhi)_
- 联系方式：
  - 邮箱： <EMAIL>
  - 电话：_(redacted)_

## 社区基本信息

1. 社区名称：安同开源社区 (Anthon Open Source Community, AOSC)
2. 社区官方网址： https://aosc.io/
3. 社区Logo文件： https://raw.githubusercontent.com/AOSC-Dev/LOGO/master/SVG/Logo%20of%20Anthon%20Open%20Source%20Community.svg
4. 社区简短描述（30 字以内）：AOSC 是围绕 Linux 发行版 AOSC OS 形成的开源社区。
5. 社区成段描述：安同开源社区（Anthon Open Source Community, 简称 AOSC）是围绕社区项目 AOSC OS 形成的开源社区。AOSC OS 是一款遵循简约、本地化、包容、可持续及精制原则独立开发的通用 Linux 发行版，致力于改善开源软件的使用体验。
6. 社区邮件列表（用于帮助学生了解社区，一般为开发者邮箱列表）： <EMAIL>
7. 社区官方公共联系邮箱： <EMAIL>
8. 技术标签：Linux、RISC-V、内核、嵌入式、阿里平头哥 (T-Head)、Rust、C、Python、Bash、GTK、本地化 (L10n)、Dpkg 包管理
9. 专注领域：Linux 发行版、中文本地化
10. 社区维护的开源项目：
  - 项目名称：AOSC OS
    项目仓库地址：https://github.com/AOSC-Dev/aosc-os-abbs
    开源协议：多种开源协议
  - 项目名称：Autobuild 3
    项目仓库地址：https://github.com/AOSC-Dev/autobuild3
    开源协议：GNU GPLv2
  - 项目名称：Ciel-rs (Ciel 3)
    项目仓库地址：https://github.com/AOSC-Dev/ciel-rs
    开源协议：MIT
  - 其他项目敬请访问我们的 [GitHub 组织页](https://github.com/AOSC-Dev/)。

## 报名问卷调查

1. 您的社区参加“暑期2021”希望有什么样的收获？


在“暑期2021”中，我们希望能继续加强学生在社区中的参与度，将学生的工作进一步整合到社区的主要工作流中，并借此使更多人能深度参与社区事务。

2. 您的社区认为一个成功的“暑期2021”是什么样的？（新的贡献者，完成新的特性，编写更多的代码，更好地引导新开发人员进入开源世界，等等）

我们认为一个成功的“暑期2021”应该：

- 以合理的任务安排，使新的贡献者能给社区的诸多项目带来新的特性；
- 充实开源社区的角色，为新的潜在贡献者提供必要的平台和支持，为开源生态作出一点微小的贡献；

3. 今年有多少潜在导师同意参与指导该项目？

五个。

4. 您的社区将如何让导师与学生保持互动？

我们计划要求学生和导师的交流加入到社区的主消息流中。在“暑期 2020”中，我们的导师和学生单独建立沟通的渠道，导致项目进行的后期出现进度不够透明的情况。由于所有社区成员都能浏览社区的主要消息流，在“暑期 2021”中我们将改变“暑期 2020”的做法。

5. 您的社区将如何帮助中选学生按时完成他们的项目？

我们计划：

- 为学生提供尽量专业的指导；
- 协助学生做好项目计划、密切监视进度，并视情况调整计划；
- 通过让学生体验一系列的社区工作流程，让学生意识到开源生态系统中的社区间交互的重要性、学习与上游交流的经验，借此让学生对项目产生主观能动性。

6. 您的社区是否参与了“暑期2020”？
  - 是。
  - 您的社区在“暑期2020”吸引了多少学生参与？
    - 约 12 名。
  - 在过去的60天里，参与暑期2020的学生中有多少人活跃在您的社区？
    - 0 名。
