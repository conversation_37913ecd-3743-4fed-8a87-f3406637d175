+++
title = "开源软件供应链点亮计划暑期 2020 项目"
description = ""
date = 2020-05-07T04:32:47.744Z
[taxonomies]
tags = ["ospp"]
+++

欢迎！以下是 AOSC 提供的可供参与的项目主题。您可以从中挑选并联系项目导师，也可以在社区 IRC、[Telegram 群组][tg]或邮件列表（随意）中和我们讨论您感兴趣的话题。

[tg]: https://t.me/joinchat/BMnG9zvfjCgZUTIAoycKkg

> 请先仔细阅读[学生指南](https://isrc.iscas.ac.cn/summer2020/help/student.html)。

# 发行版构建自动化：打包调度器

社区主要项目 AOSC OS 的一部分构建过程仍然依赖手工操作。即使大部分繁杂的工作已经能为数个社区项目很好地完成，AOSC OS 仍然面临着社区人手严重不足而带来的更新速度不足及潜在质量缺陷问题。于是我们希望建立一套切合社区实际情况的，能将发行版构建 95% 的操作自动化的基础设施来解决上述问题，以求 AOSC OS 的可持续发展。

目前分布式打包方案中包括构建机、调度器、软件包仓库管理和触发器四个部分，其中构建机和调度器是最重要的部分。本项目为编写一套功能较为完整的软件包构建调度器，以实现像 BOINC 分布式科学计算那样，自动分配打包任务、回收二进制软件包和打包时的日志和统计信息。调度器的调度模型、依赖解析已经实现，需要设计并实现的是接口和管理功能。

- 项目难度：高
- 项目社区导师：Dingyuan Wang
- 导师联系方式：<EMAIL>
- 项目产出要求：
  - 主要目标：实现打包任务的分配和回收
    - 打包任务元数据包和编译结果二进制包的管理
    - 编译过程日志和统计数据的管理
    - 设计并实现本地传输接口和 HTTP 接口
    - 密码学双向验签、错误处理和汇报机制
    - 可选/预留：软件包测试管理
  - 次要目标：实现调度器的前端可视化界面
    - 实现打包统计数据的可视化
    - 实现实时观看输出日志
    - 简洁明瞭实用即可
- 项目技术要求：
  - 熟悉 Python 3 编程语言（主要开发语言）
  - 会使用 SQLite 或 PostgreSQL 数据库
  - 了解常见 Linux 发行版的系统结构及其工具
  - 会使用 Tornado 或其他 Python Web 框架
- 相关的仓库：
  - https://github.com/AOSC-Dev/aoinb （自动化项目主仓库）
  - https://github.com/AOSC-Dev/abbs-dep （依赖解析）
  - https://github.com/AOSC-Dev/ciel （基于 Systemd 容器和 Overlayfs 的容器化构建设施）
  - https://github.com/AOSC-Dev/acbs （使用构建定义树的构建设施）
  - https://github.com/AOSC-Dev/autobuild3 （使用单个软件包构建定义的构建设施）
- 开源协议：[GPL 2.0](https://github.com/AOSC-Dev/aoinb/blob/master/LICENSE)

# 发行版构建自动化：自动化测试框架

目前 AOSC 的软件包存在一定的质量问题，且已经有了初步的[排查方案](https://packages.aosc.io/qa/)。我们希望在编译时或编译后主动地将质量问题检测出来，停止编译和发布。本项目需要在当前的打包流程中增加测试环节来确保软件包的质量。

- 项目难度：中
- 项目社区导师：Dingyuan Wang
- 导师联系方式：<EMAIL>
- 合作导师联系方式：白铭骢 <<<EMAIL>>>
- 项目产出要求：实现自动测试并汇报软件包质量问题
  - 实现在编译的各个环节对[已定义问题](@/developer/packaging/qa-issue-codes.md)的检测
  - 输出人和机器可读的测试报告（可以分开）
  - 实现软件包的安装测试（检查依赖）
  - 实现对命令行程序的冒烟测试
  - 实现对图形界面程序的冒烟测试（主要难点）
  - 在已有软件包的打包元数据中添加测试方法的描述
    - 尽量能半自动地添加，至少100个
- 项目技术要求：
  - 熟悉 Debian 软件包管理
  - 会使用 Bash、Python、Tcl 等脚本编程语言
  - 了解软件测试方法
- 相关的仓库：
  - https://github.com/AOSC-Dev/p-vector (软件包仓库管理，包括质量问题扫描)
  - https://github.com/AOSC-Dev/autobuild3 （使用单个软件包构建定义的构建设施，有些功能需要在此添加）
  - https://github.com/AOSC-Dev/packages-site （软件包网站）
- 开源协议：[GPL 2.0](https://github.com/AOSC-Dev/aoinb/blob/master/LICENSE)

# 安装程序 DeployKit 的实现

社区项目 [DeployKit][dk] 是未来 AOSC OS 的安装和恢复程序。该程序有两种模式：

1. 作为安装向导指引用户正确地安装 AOSC OS；
2. 作为恢复向导指引用户在轻微的系统配置错误中恢复系统；

目前，DeployKit 使用 [GTK][gtk] 3 和 [Vala 编程语言][vala]基本实现了安装向导的图形用户界面，但尚未实现安装向导的实际操作部分（即 DeployKit 还只是个“空壳”）。本项目的目标是按照社区提供的 [AOSC OS 安装指引][inst-guide]，将这部分原本需要手动操作的安装流程实现到 DeployKit 上。

- 项目难度：中
- 项目社区导师：黎民雍（以俊德）
- 导师联系方式：<EMAIL>
- 项目产出要求：
  - 实现 DeployKit 的系统安装部分
  - 实现安装过程在图形用户界面上的可视化（进度展示）
- 项目技术要求：
  - 了解基本的 Linux 命令
  - 了解 Vala 或其类似编程语言，如 C# 和 Java
  - 了解 GObject、GLib 和 GTK 编程
- 相关的仓库：
  - https://github.com/AOSC-Dev/DeployKit
- 开源协议：[MIT](https://github.com/AOSC-Dev/DeployKit/blob/master/COPYING)

[dk]: https://github.com/AOSC-Dev/DeployKit
[gtk]: https://www.gtk.org/
[vala]: https://wiki.gnome.org/Projects/Vala
[inst-guide]: @/aosc-os/installation/manual/amd64.md

<!--

# AOSC OS MIPS 架构移植

除 x86_64 外，AOSC OS 还支持多种处理器架构，包括 ARM 和 PowerPC。MIPS 支持曾在 2015 年到 2017 年间活跃维护，之后由于主要维护者（咳）的时间关系，这一移植没有继续下去。AOSC 希望重新启动 MIPS 这一处理器架构的移植和维护，并针对 MIPS 阵营中个人计算机设备市场保有量较大的[龙芯处理器][ls]进行一定程度上的优化。

- 项目难度：中
- 项目社区导师：黎民雍（以俊德）
- 导师联系方式：<EMAIL>
- 项目产出要求：
  - 基于 LFS Bootstrap 出 AOSC OS 在 MIPS 处理器上的基础系统（Base）
- 项目技术要求：
  - 了解从源码构建 Linux (LFS)
  - 了解 Linux 中从源码构建软件的操作（例如 Autotools、GNU Make 等的基础用法）
- 相关的仓库：
  - https://github.com/AOSC-Dev/aosc-os-abbs （AOSC OS 软件包定义和构建脚本仓库）

[ls]: http://loongson.cn/

-->

# 自由及开源软件简中本地化工作

当前我国国内有相当数量的 Linux 及各大开源或自由软件 (F/OSS) 使用者，但在使用过程中，用户们不难发现，各种软件的简中翻译及本地化质量参差不齐，错漏繁多。尤其主流桌面环境如 GNOME 及 KDE 简中翻译率并不理想；而又有一些如 NetSurf 的知名开源项目则完全没有翻译。该项目的主要目的是改善当前本地化质量及覆盖率。

- 项目难度：低
- 项目社区导师：白铭骢
- 导师联系方式：<EMAIL>
- 合作导师联系方式：刘子兴 <<<EMAIL>>>
- 项目产出要求：
  - 完善（或改善，如时间不足）现有开源软件的简中翻译，包括但不限于 CUPS、GNOME、Plasma、MATE Desktop 及 NetSurf（其余项目根据考察决定）。
  - 审阅并修改现行[大陆简中自由软件本地化工作指南（1.5.4 版）][l10n-guide]，修改后通知各大陆简中 (zh_CN) 翻译小组及社区。
- 项目技术要求：
  - 通读[大陆简中自由软件本地化工作指南（1.5.4 版）][l10n-guide]，熟知大陆简中标点、句式及选词规范及技巧。
  - 了解主要本地化软件框架（如 [GNU Gettext][gettext]）及工具（如 [Poedit][poedit] 及 [Lokalize][lokalize]。
  - 其余工作流程及技巧将于项目期间沟通及培训。
- 相关的仓库：
  - https://github.com/AOSC-Dev/translations
- 开源协议：视上游项目而定

[l10n-guide]: https://repo.aosc.io/aosc-l10n/zh_CN_l10n_1.5.4.pdf
[gettext]: http://www.gnu.org/software/gettext/
[poedit]: https://poedit.net/
[lokalize]: https://kde.org/applications/office/org.kde.lokalize/


# 为 libinput 框架实现“划圈滚动 (Circular Scrolling)”

在 Wayland 及如 GNOME 等现代桌面的推动作用下，libinput 即将取代当前诸多碎片化的 X11 输入驱动（如 `evdev` 及 `synaptics`，甚至是更老的 `mouse` 及 `keyboard`），为用户及开发者带来一定便利。但在 libinput 的实现过程中，上游开发者由于人力有限，选择抛弃了一些他们主观认为不常见的硬件特性的支持。而被抛弃的特性之一是“划圈滚动”，即在触摸板上用手指划圈滚动（类似 iPod 的操作），这一特性在滚动长页面时可有效提高操作精度并降低疲劳。

- 项目难度：中
- 项目社区导师：白铭骢
- 导师联系方式：<EMAIL>
- 合作导师联系方式：刘子兴 <<<EMAIL>>>
- 项目产出要求：
	- 基于先前 [xf86-input-synaptics][synaptics] 的代码移植 `circular-scrolling` 特性至 [libinput][libinput] 上游。
- 项目技术要求：
	- 具有一定上游参与经验，可流畅与其他上游开发者用英文进行交流。
  - 分析项目 API 并编写及移植 C 代码。
  - 编写特性测试模组。
- 相关的仓库：
	- https://github.com/freedesktop/xorg-xf86-input-synaptics
  - https://gitlab.freedesktop.org/libinput/libinput
- 开源协议：[MIT](https://gitlab.freedesktop.org/libinput/libinput/-/blob/master/COPYING)

[synaptics]: https://github.com/freedesktop/xorg-xf86-input-synaptics
[libinput]: https://gitlab.freedesktop.org/libinput/libinput

# 半自动软件包退休、封存与整理系统

软件包更新在现代社会已经不再是什么稀奇事。每天，无数软件包获得更新，有些是修复漏洞，有些是增加功能，还有些是性能优化等等。然而，并不是每一个软件包都有充分通过回归测试（Regression test），而有些更新则是主动放弃对于旧硬体的支援。由于系统的软件包仓库亦为业余爱好者维护，打包者的技术、使用的平台与配置等等亦可能导致部分软件包更新后损坏。

作为一个由历史专家带领的专业考古社区，我们提供了大量的软件包供各种不同时代与架构的硬体使用。以往，为了保证大家可以方便的切换各个版本的软件包，大量的老包被留在了系统的软件源中。再加上 AOSC 支援的架构数量在某段时间的爆发，于 2017 年时，我们的软件包仓库达到了惊人的数十 GiB 。庞大的体积对于我们自身以及各种收录了我们的开源镜像站均造成了压力。我们因此启动了Anthon Optical Storage Center (AOSC Archive) 项目，将过去的软件包从软件源中移除并收录进入 Blu-ray Disc 进行冷储存。然而使用冷储存造成的不便亦是显而易见的。如果出于各种原因，我们需要取回一个软件包进行测试，那么将会需要比较费事的人工寻找目录并取回对应光盘。另外，冷储存的成本亦是比较高的。在近期的一次检查时，我们发现有约 1%-5% 的包可能由于各种原因，在收录进入 BD 时出现了重复收录的情况。这些重复收录的包既提高了维护成本，又不具有价值。同样，整理需要取回的软件包目前也是一个非常耗费人力的工作。每次退休包时，需要寻找每个架构中有收到更新的包，并移除老包。需要把这些包适当分类并分卷放好，以便于未来取回时不需要跨越多张光盘。这一个项目就是为了解决该问题而产生的。

我们希望这个系统：

- 可以较为方便的将软件包退休，并且避免不小心移除不应当被移除的软件包。
- 可以提供检查重复的功能，以避免将一个软件包重复退休多次，白白占用资源。
- 可以提供一个软件包搜寻功能，可以方便的告知用户，需要的软件包有哪些版本，分别位于哪些光盘中。
- 可以在未来用在许多发行版上，让每个发行版都可以留下过去的软件包，在未来给大家的老机器使用。

另外，我们还希望这个系统可以快速提供一个「最小软件仓库」，仅包含最新版本的软件包，占用最少的空间。

- 项目难度：低
- 项目社区导师：张顺然 _Staph. aureus_
- 导师联系方式：<EMAIL>
- 项目产出要求：
	* 提供一个程序用于查询哪些软件源中的包可以被封存，哪些由于已封存，可以直接丢弃，哪些应当留在仓库中
	* 提供一个程序用于封存软件包：移除以及分类归纳，可能的话，最终生成 Blu-ray ISO
	* 提供一个稳定且高效的 API 可以方便的使用程式查询已封存软件包资讯
	* 提供一个较为美观的用户介面用于查询已封存软件包资讯
	* 如果有机会，提供一个老包请求网站，允许部分人上传包给请求者下载
- 项目技术要求：
	* 封存部分如果可能，使用较少的非标准库
	* 尽量做到无人工介入完成包退休封存与整理工作
	* 软件包查询可以使用很少系统资源完成
	* 这个系统应当可以较容易的应用于其他发行版的软件仓库
- 相关的开源软件仓库列表：
  - https://github.com/AOSC-Dev/aosc-archive
- 开源协议：GPL v3 或更高版本

<!--

---

以下内容由主办方提供

# 说明

1. 项目设置说明
   - 项目难度分为高、中、低三档，对应税前奖金分别为高（12000 元）、中（9000 元）、低（6000 元）。
   - 难度分级由社区导师根据项目任务自行决定。难度参考标准：  
     - 高：奖金 12000
       - 目标：能力相当于编程能力很强的计算机专业的研究生
       - 典型高难度是优化类的工作，提高时间、降低内存占用、提供性能等。
     - 中：奖金 9000
       - 目标：能力相当于研一、研二计算机专业的学生
     - 低：奖金 6000
       - 目标：能力相当于大三、大四计算机专业的学生
     - 注：以上奖金额度为税前金额。
2. 项目类别说明
   “暑期2020” 活动优先支持开发类项目，同时兼顾各类有利于社区发展的项目，例如，技术文档汉化等，非开发类项目预估总占比不超过 20%
3. 项目奖励说明
   项目奖金分 2 笔发放：中期达标 50%，最终结果分通过或不通过，由主办方和社区导师根据实际项目完成程度和完成质量评判给出。
4. 项目参与人限制说明  
   - 每个项目最终确定唯一一位学生作为中选者参与开发
   - 建议每位社区导师负责的项目任务不超过 3 个
5. 项目成果相关知识产权说明  
   学生在中选后相关的开发工作，需满足提供该项目社区的规定，具体要求由该社区负责在社区活动页面说明，例如要求学生签订 CLA、申明对应的成果的开源协议等
6. 项目信息说明  
   社区除需要收集汇总项目清单及详细信息提交给“暑期2020”工作组外，还需要提供本社区的介绍信息（见社区信息），用于活动的统一宣传。

# 项目任务示例

**说明**：**为便于学生理解，建议项目详情至少要有中文描述**

注意：下面的“精简版的树莓派镜像”只是一个例子，不是真实需求。请把需求写在示例后面。所有项目需求中，都需要留下有效的联系方式（姓名+邮箱）。

1. 项目标题：精简版的树莓派镜像
2. 项目描述：树莓派（英语：Raspberry Pi）是基于 Linux 的单片机电脑，目的是以低价硬件及自由软件促进学校的基本计算机科学教育。树莓派需要刷写文件系统镜像来实现启动，镜像文件常常都较大，不利于快速分发和安装，本项目目标是实现一个小于 NNN MB 的树莓派镜像，并能够通过 DNF 安装软件源中更多的软件进来。
3. 项目难度：高
4. 项目社区导师：姓名 或 ID
5. 导师联系方式：电子邮箱
6. 合作导师联系方式（选填）：ID或姓名，电子邮箱
7. 项目产出要求：
   - 小于 NNN MB 的树莓派镜像，该镜像可刷写在树莓派 Pi 4 上
   - 镜像中版本号信息
   - 镜像支持 DNF 安装软件源中的软件
8. 项目技术要求：
   - 基本的 Linux 命令
   - DNF/RPM 包管理
   - 具备一种脚本语言，如 Python、Bash script 等
   - 压缩算法
9. 相关的开源软件仓库列表：
   - https://gitee.com/openeuler/raspberrypi
   - https://gitee.com/openeuler/raspberrypi-kernel

-->
